{"ast": null, "code": "import{get,post,put,del}from'../api/apiClient';const CONTRACT_URL='/api/customer-contract';const JOB_DETAIL_URL='/api/job-detail';const WORK_SHIFT_URL='/api/work-shift';export const contractService={// Contract operations\ngetAllContracts:async()=>{const contracts=await get(CONTRACT_URL);// Ensure each contract has customer name\nfor(const contract of contracts){if(!contract.customerName&&contract.customerId){try{const customerResponse=await get(\"/api/customer/\".concat(contract.customerId));if(customerResponse&&customerResponse.fullName){contract.customerName=customerResponse.fullName;}}catch(error){console.error(\"Error fetching customer details for contract #\".concat(contract.id,\":\"),error);}}}return contracts;},getContractById:async id=>{const contract=await get(\"\".concat(CONTRACT_URL,\"/\").concat(id));// Nếu không có tên khách hàng, thử lấy thông tin khách hàng\nif(!contract.customerName&&contract.customerId){try{const customerResponse=await get(\"/api/customer/\".concat(contract.customerId));if(customerResponse&&customerResponse.fullName){contract.customerName=customerResponse.fullName;}}catch(error){console.error('Error fetching customer details:',error);}}// Đảm bảo mỗi job detail có tên loại công việc\nif(contract.jobDetails&&contract.jobDetails.length>0){for(const jobDetail of contract.jobDetails){if(!jobDetail.jobCategoryName&&jobDetail.jobCategoryId){try{const jobCategoryResponse=await get(\"/api/job-category/\".concat(jobDetail.jobCategoryId));if(jobCategoryResponse&&jobCategoryResponse.name){jobDetail.jobCategoryName=jobCategoryResponse.name;}}catch(error){console.error('Error fetching job category details:',error);}}}}return contract;},getContractsByCustomerId:async customerId=>{return get(\"\".concat(CONTRACT_URL,\"/customer/\").concat(customerId));},createContract:async contract=>{var _contract$jobDetails;console.log('🚀 Contract service: Creating contract...',{customerId:contract.customerId,totalAmount:contract.totalAmount,jobDetailsCount:((_contract$jobDetails=contract.jobDetails)===null||_contract$jobDetails===void 0?void 0:_contract$jobDetails.length)||0,startingDate:contract.startingDate,endingDate:contract.endingDate});const result=await post(CONTRACT_URL,contract);console.log('✅ Contract service: Contract created successfully:',{id:result.id,totalAmount:result.totalAmount,customerId:result.customerId});return result;},updateContract:async contract=>{return put(CONTRACT_URL,contract);},updateContractStatus:async(id,status)=>{return put(\"\".concat(CONTRACT_URL,\"/\").concat(id,\"/status?status=\").concat(status));},signContract:async(id,signedDate)=>{return put(\"\".concat(CONTRACT_URL,\"/\").concat(id,\"/sign?signedDate=\").concat(signedDate));},deleteContract:async id=>{return del(\"\".concat(CONTRACT_URL,\"/\").concat(id));},// Job Detail operations\ngetJobDetailById:async id=>{return get(\"\".concat(JOB_DETAIL_URL,\"/\").concat(id));},createJobDetail:async jobDetail=>{return post(JOB_DETAIL_URL,jobDetail);},updateJobDetail:async jobDetail=>{return put(JOB_DETAIL_URL,jobDetail);},deleteJobDetail:async id=>{return del(\"\".concat(JOB_DETAIL_URL,\"/\").concat(id));},// Work Shift operations\ngetWorkShiftById:async id=>{return get(\"\".concat(WORK_SHIFT_URL,\"/\").concat(id));},createWorkShift:async workShift=>{return post(WORK_SHIFT_URL,workShift);},updateWorkShift:async workShift=>{return put(WORK_SHIFT_URL,workShift);},deleteWorkShift:async id=>{return del(\"\".concat(WORK_SHIFT_URL,\"/\").concat(id));}};", "map": {"version": 3, "names": ["get", "post", "put", "del", "CONTRACT_URL", "JOB_DETAIL_URL", "WORK_SHIFT_URL", "contractService", "getAllContracts", "contracts", "contract", "customerName", "customerId", "customerResponse", "concat", "fullName", "error", "console", "id", "getContractById", "jobDetails", "length", "jobDetail", "jobCategoryName", "jobCategoryId", "jobCategoryResponse", "name", "getContractsByCustomerId", "createContract", "_contract$jobDetails", "log", "totalAmount", "jobDetailsCount", "startingDate", "endingDate", "result", "updateContract", "updateContractStatus", "status", "signContract", "signedDate", "deleteContract", "getJobDetailById", "createJobDetail", "updateJobDetail", "deleteJobDetail", "getWorkShiftById", "createWorkShift", "workShift", "updateWorkShift", "deleteWorkShift"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/contract/contractService.ts"], "sourcesContent": ["import { CustomerContract, JobDetail, WorkShift, Customer } from '../../models';\nimport { get, post, put, del } from '../api/apiClient';\n\ninterface JobCategory {\n  id: number;\n  name: string;\n  description?: string;\n  isDeleted?: boolean;\n  createdAt?: string;\n  updatedAt?: string;\n}\n\nconst CONTRACT_URL = '/api/customer-contract';\nconst JOB_DETAIL_URL = '/api/job-detail';\nconst WORK_SHIFT_URL = '/api/work-shift';\n\nexport const contractService = {\n  // Contract operations\n  getAllContracts: async (): Promise<CustomerContract[]> => {\n    const contracts = await get<CustomerContract[]>(CONTRACT_URL);\n\n    // Ensure each contract has customer name\n    for (const contract of contracts) {\n      if (!contract.customerName && contract.customerId) {\n        try {\n          const customerResponse = await get<Customer>(`/api/customer/${contract.customerId}`);\n          if (customerResponse && customerResponse.fullName) {\n            contract.customerName = customerResponse.fullName;\n          }\n        } catch (error) {\n          console.error(`Error fetching customer details for contract #${contract.id}:`, error);\n        }\n      }\n    }\n\n    return contracts;\n  },\n\n  getContractById: async (id: number): Promise<CustomerContract> => {\n    const contract = await get<CustomerContract>(`${CONTRACT_URL}/${id}`);\n\n    // Nếu không có tên khách hàng, thử lấy thông tin khách hàng\n    if (!contract.customerName && contract.customerId) {\n      try {\n        const customerResponse = await get<Customer>(`/api/customer/${contract.customerId}`);\n        if (customerResponse && customerResponse.fullName) {\n          contract.customerName = customerResponse.fullName;\n        }\n      } catch (error) {\n        console.error('Error fetching customer details:', error);\n      }\n    }\n\n    // Đảm bảo mỗi job detail có tên loại công việc\n    if (contract.jobDetails && contract.jobDetails.length > 0) {\n      for (const jobDetail of contract.jobDetails) {\n        if (!jobDetail.jobCategoryName && jobDetail.jobCategoryId) {\n          try {\n            const jobCategoryResponse = await get<JobCategory>(`/api/job-category/${jobDetail.jobCategoryId}`);\n            if (jobCategoryResponse && jobCategoryResponse.name) {\n              jobDetail.jobCategoryName = jobCategoryResponse.name;\n            }\n          } catch (error) {\n            console.error('Error fetching job category details:', error);\n          }\n        }\n      }\n    }\n\n    return contract;\n  },\n\n  getContractsByCustomerId: async (customerId: number): Promise<CustomerContract[]> => {\n    return get<CustomerContract[]>(`${CONTRACT_URL}/customer/${customerId}`);\n  },\n\n  createContract: async (contract: CustomerContract): Promise<CustomerContract> => {\n    console.log('🚀 Contract service: Creating contract...', {\n      customerId: contract.customerId,\n      totalAmount: contract.totalAmount,\n      jobDetailsCount: contract.jobDetails?.length || 0,\n      startingDate: contract.startingDate,\n      endingDate: contract.endingDate\n    });\n\n    const result = await post<CustomerContract>(CONTRACT_URL, contract);\n\n    console.log('✅ Contract service: Contract created successfully:', {\n      id: result.id,\n      totalAmount: result.totalAmount,\n      customerId: result.customerId\n    });\n\n    return result;\n  },\n\n  updateContract: async (contract: CustomerContract): Promise<CustomerContract> => {\n    return put<CustomerContract>(CONTRACT_URL, contract);\n  },\n\n  updateContractStatus: async (id: number, status: number): Promise<CustomerContract> => {\n    return put<CustomerContract>(`${CONTRACT_URL}/${id}/status?status=${status}`);\n  },\n\n  signContract: async (id: number, signedDate: string): Promise<CustomerContract> => {\n    return put<CustomerContract>(`${CONTRACT_URL}/${id}/sign?signedDate=${signedDate}`);\n  },\n\n  deleteContract: async (id: number): Promise<void> => {\n    return del<void>(`${CONTRACT_URL}/${id}`);\n  },\n\n  // Job Detail operations\n  getJobDetailById: async (id: number): Promise<JobDetail> => {\n    return get<JobDetail>(`${JOB_DETAIL_URL}/${id}`);\n  },\n\n  createJobDetail: async (jobDetail: JobDetail): Promise<JobDetail> => {\n    return post<JobDetail>(JOB_DETAIL_URL, jobDetail);\n  },\n\n  updateJobDetail: async (jobDetail: JobDetail): Promise<JobDetail> => {\n    return put<JobDetail>(JOB_DETAIL_URL, jobDetail);\n  },\n\n  deleteJobDetail: async (id: number): Promise<void> => {\n    return del<void>(`${JOB_DETAIL_URL}/${id}`);\n  },\n\n  // Work Shift operations\n  getWorkShiftById: async (id: number): Promise<WorkShift> => {\n    return get<WorkShift>(`${WORK_SHIFT_URL}/${id}`);\n  },\n\n  createWorkShift: async (workShift: WorkShift): Promise<WorkShift> => {\n    return post<WorkShift>(WORK_SHIFT_URL, workShift);\n  },\n\n  updateWorkShift: async (workShift: WorkShift): Promise<WorkShift> => {\n    return put<WorkShift>(WORK_SHIFT_URL, workShift);\n  },\n\n  deleteWorkShift: async (id: number): Promise<void> => {\n    return del<void>(`${WORK_SHIFT_URL}/${id}`);\n  }\n};\n"], "mappings": "AACA,OAASA,GAAG,CAAEC,IAAI,CAAEC,GAAG,CAAEC,GAAG,KAAQ,kBAAkB,CAWtD,KAAM,CAAAC,YAAY,CAAG,wBAAwB,CAC7C,KAAM,CAAAC,cAAc,CAAG,iBAAiB,CACxC,KAAM,CAAAC,cAAc,CAAG,iBAAiB,CAExC,MAAO,MAAM,CAAAC,eAAe,CAAG,CAC7B;AACAC,eAAe,CAAE,KAAAA,CAAA,GAAyC,CACxD,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAT,GAAG,CAAqBI,YAAY,CAAC,CAE7D;AACA,IAAK,KAAM,CAAAM,QAAQ,GAAI,CAAAD,SAAS,CAAE,CAChC,GAAI,CAACC,QAAQ,CAACC,YAAY,EAAID,QAAQ,CAACE,UAAU,CAAE,CACjD,GAAI,CACF,KAAM,CAAAC,gBAAgB,CAAG,KAAM,CAAAb,GAAG,kBAAAc,MAAA,CAA4BJ,QAAQ,CAACE,UAAU,CAAE,CAAC,CACpF,GAAIC,gBAAgB,EAAIA,gBAAgB,CAACE,QAAQ,CAAE,CACjDL,QAAQ,CAACC,YAAY,CAAGE,gBAAgB,CAACE,QAAQ,CACnD,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,kDAAAF,MAAA,CAAkDJ,QAAQ,CAACQ,EAAE,MAAKF,KAAK,CAAC,CACvF,CACF,CACF,CAEA,MAAO,CAAAP,SAAS,CAClB,CAAC,CAEDU,eAAe,CAAE,KAAO,CAAAD,EAAU,EAAgC,CAChE,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAV,GAAG,IAAAc,MAAA,CAAsBV,YAAY,MAAAU,MAAA,CAAII,EAAE,CAAE,CAAC,CAErE;AACA,GAAI,CAACR,QAAQ,CAACC,YAAY,EAAID,QAAQ,CAACE,UAAU,CAAE,CACjD,GAAI,CACF,KAAM,CAAAC,gBAAgB,CAAG,KAAM,CAAAb,GAAG,kBAAAc,MAAA,CAA4BJ,QAAQ,CAACE,UAAU,CAAE,CAAC,CACpF,GAAIC,gBAAgB,EAAIA,gBAAgB,CAACE,QAAQ,CAAE,CACjDL,QAAQ,CAACC,YAAY,CAAGE,gBAAgB,CAACE,QAAQ,CACnD,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAC1D,CACF,CAEA;AACA,GAAIN,QAAQ,CAACU,UAAU,EAAIV,QAAQ,CAACU,UAAU,CAACC,MAAM,CAAG,CAAC,CAAE,CACzD,IAAK,KAAM,CAAAC,SAAS,GAAI,CAAAZ,QAAQ,CAACU,UAAU,CAAE,CAC3C,GAAI,CAACE,SAAS,CAACC,eAAe,EAAID,SAAS,CAACE,aAAa,CAAE,CACzD,GAAI,CACF,KAAM,CAAAC,mBAAmB,CAAG,KAAM,CAAAzB,GAAG,sBAAAc,MAAA,CAAmCQ,SAAS,CAACE,aAAa,CAAE,CAAC,CAClG,GAAIC,mBAAmB,EAAIA,mBAAmB,CAACC,IAAI,CAAE,CACnDJ,SAAS,CAACC,eAAe,CAAGE,mBAAmB,CAACC,IAAI,CACtD,CACF,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC9D,CACF,CACF,CACF,CAEA,MAAO,CAAAN,QAAQ,CACjB,CAAC,CAEDiB,wBAAwB,CAAE,KAAO,CAAAf,UAAkB,EAAkC,CACnF,MAAO,CAAAZ,GAAG,IAAAc,MAAA,CAAwBV,YAAY,eAAAU,MAAA,CAAaF,UAAU,CAAE,CAAC,CAC1E,CAAC,CAEDgB,cAAc,CAAE,KAAO,CAAAlB,QAA0B,EAAgC,KAAAmB,oBAAA,CAC/EZ,OAAO,CAACa,GAAG,CAAC,2CAA2C,CAAE,CACvDlB,UAAU,CAAEF,QAAQ,CAACE,UAAU,CAC/BmB,WAAW,CAAErB,QAAQ,CAACqB,WAAW,CACjCC,eAAe,CAAE,EAAAH,oBAAA,CAAAnB,QAAQ,CAACU,UAAU,UAAAS,oBAAA,iBAAnBA,oBAAA,CAAqBR,MAAM,GAAI,CAAC,CACjDY,YAAY,CAAEvB,QAAQ,CAACuB,YAAY,CACnCC,UAAU,CAAExB,QAAQ,CAACwB,UACvB,CAAC,CAAC,CAEF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAlC,IAAI,CAAmBG,YAAY,CAAEM,QAAQ,CAAC,CAEnEO,OAAO,CAACa,GAAG,CAAC,oDAAoD,CAAE,CAChEZ,EAAE,CAAEiB,MAAM,CAACjB,EAAE,CACba,WAAW,CAAEI,MAAM,CAACJ,WAAW,CAC/BnB,UAAU,CAAEuB,MAAM,CAACvB,UACrB,CAAC,CAAC,CAEF,MAAO,CAAAuB,MAAM,CACf,CAAC,CAEDC,cAAc,CAAE,KAAO,CAAA1B,QAA0B,EAAgC,CAC/E,MAAO,CAAAR,GAAG,CAAmBE,YAAY,CAAEM,QAAQ,CAAC,CACtD,CAAC,CAED2B,oBAAoB,CAAE,KAAAA,CAAOnB,EAAU,CAAEoB,MAAc,GAAgC,CACrF,MAAO,CAAApC,GAAG,IAAAY,MAAA,CAAsBV,YAAY,MAAAU,MAAA,CAAII,EAAE,oBAAAJ,MAAA,CAAkBwB,MAAM,CAAE,CAAC,CAC/E,CAAC,CAEDC,YAAY,CAAE,KAAAA,CAAOrB,EAAU,CAAEsB,UAAkB,GAAgC,CACjF,MAAO,CAAAtC,GAAG,IAAAY,MAAA,CAAsBV,YAAY,MAAAU,MAAA,CAAII,EAAE,sBAAAJ,MAAA,CAAoB0B,UAAU,CAAE,CAAC,CACrF,CAAC,CAEDC,cAAc,CAAE,KAAO,CAAAvB,EAAU,EAAoB,CACnD,MAAO,CAAAf,GAAG,IAAAW,MAAA,CAAUV,YAAY,MAAAU,MAAA,CAAII,EAAE,CAAE,CAAC,CAC3C,CAAC,CAED;AACAwB,gBAAgB,CAAE,KAAO,CAAAxB,EAAU,EAAyB,CAC1D,MAAO,CAAAlB,GAAG,IAAAc,MAAA,CAAeT,cAAc,MAAAS,MAAA,CAAII,EAAE,CAAE,CAAC,CAClD,CAAC,CAEDyB,eAAe,CAAE,KAAO,CAAArB,SAAoB,EAAyB,CACnE,MAAO,CAAArB,IAAI,CAAYI,cAAc,CAAEiB,SAAS,CAAC,CACnD,CAAC,CAEDsB,eAAe,CAAE,KAAO,CAAAtB,SAAoB,EAAyB,CACnE,MAAO,CAAApB,GAAG,CAAYG,cAAc,CAAEiB,SAAS,CAAC,CAClD,CAAC,CAEDuB,eAAe,CAAE,KAAO,CAAA3B,EAAU,EAAoB,CACpD,MAAO,CAAAf,GAAG,IAAAW,MAAA,CAAUT,cAAc,MAAAS,MAAA,CAAII,EAAE,CAAE,CAAC,CAC7C,CAAC,CAED;AACA4B,gBAAgB,CAAE,KAAO,CAAA5B,EAAU,EAAyB,CAC1D,MAAO,CAAAlB,GAAG,IAAAc,MAAA,CAAeR,cAAc,MAAAQ,MAAA,CAAII,EAAE,CAAE,CAAC,CAClD,CAAC,CAED6B,eAAe,CAAE,KAAO,CAAAC,SAAoB,EAAyB,CACnE,MAAO,CAAA/C,IAAI,CAAYK,cAAc,CAAE0C,SAAS,CAAC,CACnD,CAAC,CAEDC,eAAe,CAAE,KAAO,CAAAD,SAAoB,EAAyB,CACnE,MAAO,CAAA9C,GAAG,CAAYI,cAAc,CAAE0C,SAAS,CAAC,CAClD,CAAC,CAEDE,eAAe,CAAE,KAAO,CAAAhC,EAAU,EAAoB,CACpD,MAAO,CAAAf,GAAG,IAAAW,MAAA,CAAUR,cAAc,MAAAQ,MAAA,CAAII,EAAE,CAAE,CAAC,CAC7C,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}