{"ast": null, "code": "import axios from 'axios';\n\n// Kiểm tra môi trường và cấu hình URL phù hợp\nconst getBaseUrl = () => {\n  // Nếu đang chạy trong môi trường phát triển (localhost)\n  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {\n    // Kiểm tra xem API Gateway có hoạt động không\n    const apiGatewayUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080';\n    console.log('Using API Gateway URL:', apiGatewayUrl);\n    return apiGatewayUrl;\n  }\n\n  // Nếu đang chạy trong môi trường production, sử dụng URL tương đối\n  return '';\n};\n\n// Create a base API client instance\nconst apiClient = axios.create({\n  baseURL: getBaseUrl(),\n  // API gateway URL\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request\n  },\n  timeout: 30000,\n  // Tăng timeout lên 30 seconds\n  withCredentials: false // Không gửi cookie trong cross-origin requests\n});\n\n// Request interceptor for API calls\napiClient.interceptors.request.use(config => {\n  var _config$method;\n  // Log the request for debugging\n  console.log(`API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n  return config;\n}, error => {\n  console.error('Request error:', error);\n  return Promise.reject(error);\n});\n\n// Response interceptor for API calls\napiClient.interceptors.response.use(response => {\n  // Log successful responses for debugging\n  console.log(`API Response: ${response.status} ${response.config.url}`, response.data);\n\n  // Kiểm tra nếu response là JSON hợp lệ\n  try {\n    if (typeof response.data === 'string' && response.data.trim() !== '') {\n      console.warn('Response is string, attempting to parse as JSON:', response.data);\n      response.data = JSON.parse(response.data);\n    }\n  } catch (e) {\n    console.error('Failed to parse response data as JSON:', e);\n    // Don't throw error for successful responses, just log the warning\n  }\n\n  // Ensure we have a valid response for successful operations\n  if (response.status >= 200 && response.status < 300) {\n    console.log('✅ Successful API response:', {\n      status: response.status,\n      url: response.config.url,\n      method: response.config.method,\n      dataType: typeof response.data,\n      hasData: !!response.data\n    });\n  }\n  return response;\n}, error => {\n  // Handle errors globally\n  if (axios.isAxiosError(error)) {\n    var _error$response, _error$config, _error$config2, _error$response2, _error$response3, _error$response4, _error$response5, _error$response6, _error$response7, _error$response8, _error$response9;\n    const errorInfo = {\n      message: error.message,\n      status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n      url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n      method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method,\n      data: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data,\n      code: error.code\n    };\n    console.error('❌ API Error:', errorInfo);\n\n    // Xử lý thông báo lỗi từ backend\n    if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && _error$response3.data) {\n      // Nếu backend trả về thông báo lỗi cụ thể\n      if (typeof error.response.data === 'string') {\n        error.message = error.response.data;\n      } else if (error.response.data.message) {\n        error.message = error.response.data.message;\n      } else if (error.response.data.error) {\n        error.message = error.response.data.error;\n      }\n    }\n\n    // Log specific error types for debugging\n    if (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 200 || ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) === 201) {\n      console.warn('⚠️ Received error for successful status code:', error.response.status);\n      console.warn('This might indicate a response parsing issue');\n    }\n\n    // Chi tiết hơn về các loại lỗi\n    if (error.code === 'ECONNABORTED') {\n      console.error('Request timeout. The server took too long to respond.');\n      error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';\n    } else if (error.message.includes('Network Error') || !error.response) {\n      console.error('Network error. Please check your connection or the server might be down.');\n\n      // Kiểm tra lỗi CORS\n      if (error.message.includes('CORS')) {\n        console.error('CORS error detected. This might be a cross-origin issue.');\n        error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';\n      } else {\n        error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';\n      }\n    } else if (((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.status) === 404) {\n      var _error$response$data;\n      if (!((_error$response$data = error.response.data) !== null && _error$response$data !== void 0 && _error$response$data.message)) {\n        error.message = 'Không tìm thấy tài nguyên yêu cầu.';\n      }\n    } else if (((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status) === 500) {\n      var _error$response$data2;\n      if (!((_error$response$data2 = error.response.data) !== null && _error$response$data2 !== void 0 && _error$response$data2.message)) {\n        error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      }\n    } else if (((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.status) === 403) {\n      var _error$response$data3;\n      if (!((_error$response$data3 = error.response.data) !== null && _error$response$data3 !== void 0 && _error$response$data3.message)) {\n        error.message = 'Bạn không có quyền truy cập tài nguyên này.';\n      }\n    } else if (((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.status) === 400) {\n      var _error$response$data4;\n      // Lỗi validation hoặc business logic từ backend\n      if (!((_error$response$data4 = error.response.data) !== null && _error$response$data4 !== void 0 && _error$response$data4.message)) {\n        error.message = 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      }\n    }\n  } else {\n    console.error('Unexpected error:', error);\n  }\n  return Promise.reject(error);\n});\n\n// Generic GET request\nexport const get = async (url, config) => {\n  try {\n    console.log(`Making GET request to: ${getBaseUrl()}${url}`);\n    const response = await apiClient.get(url, config);\n    console.log(`✅ GET request to ${url} succeeded with status:`, response.status);\n    return response.data;\n  } catch (error) {\n    console.error(`❌ GET request failed for ${url}:`, error);\n    throw error;\n  }\n};\n\n// Generic POST request\nexport const post = async (url, data, config) => {\n  try {\n    console.log(`Making POST request to: ${getBaseUrl()}${url}`, data);\n    const response = await apiClient.post(url, data, config);\n    console.log(`✅ POST request to ${url} succeeded with status:`, response.status, response.data);\n\n    // Ensure we return the response data for successful operations\n    if (response.status >= 200 && response.status < 300) {\n      return response.data;\n    } else {\n      // This shouldn't happen with axios, but just in case\n      throw new Error(`Unexpected response status: ${response.status}`);\n    }\n  } catch (error) {\n    console.error(`❌ POST request failed for ${url}:`, error);\n\n    // For POST requests (create operations), DO NOT use fallback to prevent duplicate creation\n    // Only throw the original error to avoid duplicate records\n    if (axios.isAxiosError(error)) {\n      var _error$response0, _error$response1, _error$config3, _error$config4, _error$response10, _error$response11;\n      // Enhance error message with more details\n      const errorDetails = {\n        status: (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status,\n        statusText: (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.statusText,\n        url: (_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.url,\n        method: (_error$config4 = error.config) === null || _error$config4 === void 0 ? void 0 : _error$config4.method,\n        data: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data\n      };\n      console.error('Detailed error info:', errorDetails);\n      if ((_error$response11 = error.response) !== null && _error$response11 !== void 0 && _error$response11.data) {\n        if (typeof error.response.data === 'string') {\n          error.message = error.response.data;\n        } else if (error.response.data.message) {\n          error.message = error.response.data.message;\n        }\n      }\n    }\n    throw error;\n  }\n};\n\n// Generic PUT request\nexport const put = async (url, data, config) => {\n  try {\n    console.log(`Making PUT request to: ${getBaseUrl()}${url}`);\n    const response = await apiClient.put(url, data, config);\n    console.log(`PUT request to ${url} succeeded with status:`, response.status);\n    return response.data;\n  } catch (error) {\n    var _error$response12;\n    console.error(`PUT request failed for ${url}:`, error);\n\n    // For PUT requests (update operations), DO NOT use fallback to prevent duplicate updates\n    // Only throw the original error to avoid duplicate operations\n    if (axios.isAxiosError(error) && (_error$response12 = error.response) !== null && _error$response12 !== void 0 && _error$response12.data) {\n      if (typeof error.response.data === 'string') {\n        error.message = error.response.data;\n      } else if (error.response.data.message) {\n        error.message = error.response.data.message;\n      }\n    }\n    throw error;\n  }\n};\n\n// Generic DELETE request\nexport const del = async (url, config) => {\n  try {\n    console.log(`Making DELETE request to: ${getBaseUrl()}${url}`);\n    const response = await apiClient.delete(url, config);\n    console.log(`DELETE request to ${url} succeeded with status:`, response.status);\n    return response.data;\n  } catch (error) {\n    var _error$response13;\n    console.error(`DELETE request failed for ${url}:`, error);\n\n    // For DELETE requests, DO NOT use fallback to prevent duplicate deletions\n    // Only throw the original error to avoid duplicate operations\n    if (axios.isAxiosError(error) && (_error$response13 = error.response) !== null && _error$response13 !== void 0 && _error$response13.data) {\n      if (typeof error.response.data === 'string') {\n        error.message = error.response.data;\n      } else if (error.response.data.message) {\n        error.message = error.response.data.message;\n      }\n    }\n    throw error;\n  }\n};\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "getBaseUrl", "window", "location", "hostname", "apiGatewayUrl", "process", "env", "REACT_APP_API_URL", "console", "log", "apiClient", "create", "baseURL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "config", "_config$method", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "data", "trim", "warn", "JSON", "parse", "e", "dataType", "hasData", "isAxiosError", "_error$response", "_error$config", "_error$config2", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "_error$response6", "_error$response7", "_error$response8", "_error$response9", "errorInfo", "message", "code", "includes", "_error$response$data", "_error$response$data2", "_error$response$data3", "_error$response$data4", "get", "post", "Error", "_error$response0", "_error$response1", "_error$config3", "_error$config4", "_error$response10", "_error$response11", "errorDetails", "statusText", "put", "_error$response12", "del", "delete", "_error$response13"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/api/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// Kiểm tra môi trường và cấu hình URL phù hợp\nconst getBaseUrl = () => {\n  // Nếu đang chạy trong môi trường phát triển (localhost)\n  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {\n    // Kiểm tra xem API Gateway có hoạt động không\n    const apiGatewayUrl = process.env.REACT_APP_API_URL || 'http://localhost:8080';\n    console.log('Using API Gateway URL:', apiGatewayUrl);\n    return apiGatewayUrl;\n  }\n\n  // Nếu đang chạy trong môi trường production, sử dụng URL tương đối\n  return '';\n};\n\n\n\n// Create a base API client instance\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: getBaseUrl(), // API gateway URL\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest' // Giúp một số máy chủ nhận biết đây là AJAX request\n  },\n  timeout: 30000, // Tăng timeout lên 30 seconds\n  withCredentials: false // Không gửi cookie trong cross-origin requests\n});\n\n// Request interceptor for API calls\napiClient.interceptors.request.use(\n  (config) => {\n    // Log the request for debugging\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('Request error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for API calls\napiClient.interceptors.response.use(\n  (response) => {\n    // Log successful responses for debugging\n    console.log(`API Response: ${response.status} ${response.config.url}`, response.data);\n\n    // Kiểm tra nếu response là JSON hợp lệ\n    try {\n      if (typeof response.data === 'string' && response.data.trim() !== '') {\n        console.warn('Response is string, attempting to parse as JSON:', response.data);\n        response.data = JSON.parse(response.data);\n      }\n    } catch (e) {\n      console.error('Failed to parse response data as JSON:', e);\n      // Don't throw error for successful responses, just log the warning\n    }\n\n    // Ensure we have a valid response for successful operations\n    if (response.status >= 200 && response.status < 300) {\n      console.log('✅ Successful API response:', {\n        status: response.status,\n        url: response.config.url,\n        method: response.config.method,\n        dataType: typeof response.data,\n        hasData: !!response.data\n      });\n    }\n\n    return response;\n  },\n  (error) => {\n    // Handle errors globally\n    if (axios.isAxiosError(error)) {\n      const errorInfo = {\n        message: error.message,\n        status: error.response?.status,\n        url: error.config?.url,\n        method: error.config?.method,\n        data: error.response?.data,\n        code: error.code\n      };\n\n      console.error('❌ API Error:', errorInfo);\n\n      // Xử lý thông báo lỗi từ backend\n      if (error.response?.data) {\n        // Nếu backend trả về thông báo lỗi cụ thể\n        if (typeof error.response.data === 'string') {\n          error.message = error.response.data;\n        } else if (error.response.data.message) {\n          error.message = error.response.data.message;\n        } else if (error.response.data.error) {\n          error.message = error.response.data.error;\n        }\n      }\n\n      // Log specific error types for debugging\n      if (error.response?.status === 200 || error.response?.status === 201) {\n        console.warn('⚠️ Received error for successful status code:', error.response.status);\n        console.warn('This might indicate a response parsing issue');\n      }\n\n      // Chi tiết hơn về các loại lỗi\n      if (error.code === 'ECONNABORTED') {\n        console.error('Request timeout. The server took too long to respond.');\n        error.message = 'Yêu cầu đã hết thời gian chờ. Máy chủ mất quá nhiều thời gian để phản hồi.';\n      } else if (error.message.includes('Network Error') || !error.response) {\n        console.error('Network error. Please check your connection or the server might be down.');\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('CORS')) {\n          console.error('CORS error detected. This might be a cross-origin issue.');\n          error.message = 'Lỗi CORS: Không thể kết nối đến máy chủ do chính sách bảo mật trình duyệt.';\n        } else {\n          error.message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối của bạn hoặc máy chủ có thể đang gặp sự cố.';\n        }\n      } else if (error.response?.status === 404) {\n        if (!error.response.data?.message) {\n          error.message = 'Không tìm thấy tài nguyên yêu cầu.';\n        }\n      } else if (error.response?.status === 500) {\n        if (!error.response.data?.message) {\n          error.message = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n        }\n      } else if (error.response?.status === 403) {\n        if (!error.response.data?.message) {\n          error.message = 'Bạn không có quyền truy cập tài nguyên này.';\n        }\n      } else if (error.response?.status === 400) {\n        // Lỗi validation hoặc business logic từ backend\n        if (!error.response.data?.message) {\n          error.message = 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n        }\n      }\n    } else {\n      console.error('Unexpected error:', error);\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Generic GET request\nexport const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    console.log(`Making GET request to: ${getBaseUrl()}${url}`);\n    const response: AxiosResponse<T> = await apiClient.get(url, config);\n    console.log(`✅ GET request to ${url} succeeded with status:`, response.status);\n    return response.data;\n  } catch (error) {\n    console.error(`❌ GET request failed for ${url}:`, error);\n    throw error;\n  }\n};\n\n// Generic POST request\nexport const post = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    console.log(`Making POST request to: ${getBaseUrl()}${url}`, data);\n    const response: AxiosResponse<T> = await apiClient.post(url, data, config);\n    console.log(`✅ POST request to ${url} succeeded with status:`, response.status, response.data);\n\n    // Ensure we return the response data for successful operations\n    if (response.status >= 200 && response.status < 300) {\n      return response.data;\n    } else {\n      // This shouldn't happen with axios, but just in case\n      throw new Error(`Unexpected response status: ${response.status}`);\n    }\n  } catch (error) {\n    console.error(`❌ POST request failed for ${url}:`, error);\n\n    // For POST requests (create operations), DO NOT use fallback to prevent duplicate creation\n    // Only throw the original error to avoid duplicate records\n    if (axios.isAxiosError(error)) {\n      // Enhance error message with more details\n      const errorDetails = {\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        url: error.config?.url,\n        method: error.config?.method,\n        data: error.response?.data\n      };\n      console.error('Detailed error info:', errorDetails);\n\n      if (error.response?.data) {\n        if (typeof error.response.data === 'string') {\n          error.message = error.response.data;\n        } else if (error.response.data.message) {\n          error.message = error.response.data.message;\n        }\n      }\n    }\n    throw error;\n  }\n};\n\n// Generic PUT request\nexport const put = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    console.log(`Making PUT request to: ${getBaseUrl()}${url}`);\n    const response: AxiosResponse<T> = await apiClient.put(url, data, config);\n    console.log(`PUT request to ${url} succeeded with status:`, response.status);\n    return response.data;\n  } catch (error) {\n    console.error(`PUT request failed for ${url}:`, error);\n\n    // For PUT requests (update operations), DO NOT use fallback to prevent duplicate updates\n    // Only throw the original error to avoid duplicate operations\n    if (axios.isAxiosError(error) && error.response?.data) {\n      if (typeof error.response.data === 'string') {\n        error.message = error.response.data;\n      } else if (error.response.data.message) {\n        error.message = error.response.data.message;\n      }\n    }\n    throw error;\n  }\n};\n\n// Generic DELETE request\nexport const del = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n  try {\n    console.log(`Making DELETE request to: ${getBaseUrl()}${url}`);\n    const response: AxiosResponse<T> = await apiClient.delete(url, config);\n    console.log(`DELETE request to ${url} succeeded with status:`, response.status);\n    return response.data;\n  } catch (error) {\n    console.error(`DELETE request failed for ${url}:`, error);\n\n    // For DELETE requests, DO NOT use fallback to prevent duplicate deletions\n    // Only throw the original error to avoid duplicate operations\n    if (axios.isAxiosError(error) && error.response?.data) {\n      if (typeof error.response.data === 'string') {\n        error.message = error.response.data;\n      } else if (error.response.data.message) {\n        error.message = error.response.data.message;\n      }\n    }\n    throw error;\n  }\n};\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAA4D,OAAO;;AAE/E;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB;EACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;IACxF;IACA,MAAMC,aAAa,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;IAC9EC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEL,aAAa,CAAC;IACpD,OAAOA,aAAa;EACtB;;EAEA;EACA,OAAO,EAAE;AACX,CAAC;;AAID;AACA,MAAMM,SAAwB,GAAGX,KAAK,CAACY,MAAM,CAAC;EAC5CC,OAAO,EAAEZ,UAAU,CAAC,CAAC;EAAE;EACvBa,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE,kBAAkB;IAC5B,kBAAkB,EAAE,gBAAgB,CAAC;EACvC,CAAC;EACDC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAL,SAAS,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACV;EACAZ,OAAO,CAACC,GAAG,CAAC,iBAAAW,cAAA,GAAgBD,MAAM,CAACE,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,IAAIH,MAAM,CAACI,GAAG,EAAE,CAAC;EACzE,OAAOJ,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACThB,OAAO,CAACgB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,SAAS,CAACM,YAAY,CAACW,QAAQ,CAACT,GAAG,CAChCS,QAAQ,IAAK;EACZ;EACAnB,OAAO,CAACC,GAAG,CAAC,iBAAiBkB,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACR,MAAM,CAACI,GAAG,EAAE,EAAEI,QAAQ,CAACE,IAAI,CAAC;;EAErF;EACA,IAAI;IACF,IAAI,OAAOF,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAIF,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACpEtB,OAAO,CAACuB,IAAI,CAAC,kDAAkD,EAAEJ,QAAQ,CAACE,IAAI,CAAC;MAC/EF,QAAQ,CAACE,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACN,QAAQ,CAACE,IAAI,CAAC;IAC3C;EACF,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV1B,OAAO,CAACgB,KAAK,CAAC,wCAAwC,EAAEU,CAAC,CAAC;IAC1D;EACF;;EAEA;EACA,IAAIP,QAAQ,CAACC,MAAM,IAAI,GAAG,IAAID,QAAQ,CAACC,MAAM,GAAG,GAAG,EAAE;IACnDpB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxCmB,MAAM,EAAED,QAAQ,CAACC,MAAM;MACvBL,GAAG,EAAEI,QAAQ,CAACR,MAAM,CAACI,GAAG;MACxBF,MAAM,EAAEM,QAAQ,CAACR,MAAM,CAACE,MAAM;MAC9Bc,QAAQ,EAAE,OAAOR,QAAQ,CAACE,IAAI;MAC9BO,OAAO,EAAE,CAAC,CAACT,QAAQ,CAACE;IACtB,CAAC,CAAC;EACJ;EAEA,OAAOF,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT;EACA,IAAIzB,KAAK,CAACsC,YAAY,CAACb,KAAK,CAAC,EAAE;IAAA,IAAAc,eAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;IAC7B,MAAMC,SAAS,GAAG;MAChBC,OAAO,EAAE1B,KAAK,CAAC0B,OAAO;MACtBtB,MAAM,GAAAU,eAAA,GAAEd,KAAK,CAACG,QAAQ,cAAAW,eAAA,uBAAdA,eAAA,CAAgBV,MAAM;MAC9BL,GAAG,GAAAgB,aAAA,GAAEf,KAAK,CAACL,MAAM,cAAAoB,aAAA,uBAAZA,aAAA,CAAchB,GAAG;MACtBF,MAAM,GAAAmB,cAAA,GAAEhB,KAAK,CAACL,MAAM,cAAAqB,cAAA,uBAAZA,cAAA,CAAcnB,MAAM;MAC5BQ,IAAI,GAAAY,gBAAA,GAAEjB,KAAK,CAACG,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBZ,IAAI;MAC1BsB,IAAI,EAAE3B,KAAK,CAAC2B;IACd,CAAC;IAED3C,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAEyB,SAAS,CAAC;;IAExC;IACA,KAAAP,gBAAA,GAAIlB,KAAK,CAACG,QAAQ,cAAAe,gBAAA,eAAdA,gBAAA,CAAgBb,IAAI,EAAE;MACxB;MACA,IAAI,OAAOL,KAAK,CAACG,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;QAC3CL,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI;MACrC,CAAC,MAAM,IAAIL,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO,EAAE;QACtC1B,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO;MAC7C,CAAC,MAAM,IAAI1B,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACL,KAAK,EAAE;QACpCA,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACL,KAAK;MAC3C;IACF;;IAEA;IACA,IAAI,EAAAmB,gBAAA,GAAAnB,KAAK,CAACG,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBf,MAAM,MAAK,GAAG,IAAI,EAAAgB,gBAAA,GAAApB,KAAK,CAACG,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBhB,MAAM,MAAK,GAAG,EAAE;MACpEpB,OAAO,CAACuB,IAAI,CAAC,+CAA+C,EAAEP,KAAK,CAACG,QAAQ,CAACC,MAAM,CAAC;MACpFpB,OAAO,CAACuB,IAAI,CAAC,8CAA8C,CAAC;IAC9D;;IAEA;IACA,IAAIP,KAAK,CAAC2B,IAAI,KAAK,cAAc,EAAE;MACjC3C,OAAO,CAACgB,KAAK,CAAC,uDAAuD,CAAC;MACtEA,KAAK,CAAC0B,OAAO,GAAG,4EAA4E;IAC9F,CAAC,MAAM,IAAI1B,KAAK,CAAC0B,OAAO,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC5B,KAAK,CAACG,QAAQ,EAAE;MACrEnB,OAAO,CAACgB,KAAK,CAAC,0EAA0E,CAAC;;MAEzF;MACA,IAAIA,KAAK,CAAC0B,OAAO,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAClC5C,OAAO,CAACgB,KAAK,CAAC,0DAA0D,CAAC;QACzEA,KAAK,CAAC0B,OAAO,GAAG,4EAA4E;MAC9F,CAAC,MAAM;QACL1B,KAAK,CAAC0B,OAAO,GAAG,yFAAyF;MAC3G;IACF,CAAC,MAAM,IAAI,EAAAL,gBAAA,GAAArB,KAAK,CAACG,QAAQ,cAAAkB,gBAAA,uBAAdA,gBAAA,CAAgBjB,MAAM,MAAK,GAAG,EAAE;MAAA,IAAAyB,oBAAA;MACzC,IAAI,GAAAA,oBAAA,GAAC7B,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAAwB,oBAAA,eAAnBA,oBAAA,CAAqBH,OAAO,GAAE;QACjC1B,KAAK,CAAC0B,OAAO,GAAG,oCAAoC;MACtD;IACF,CAAC,MAAM,IAAI,EAAAJ,gBAAA,GAAAtB,KAAK,CAACG,QAAQ,cAAAmB,gBAAA,uBAAdA,gBAAA,CAAgBlB,MAAM,MAAK,GAAG,EAAE;MAAA,IAAA0B,qBAAA;MACzC,IAAI,GAAAA,qBAAA,GAAC9B,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAAyB,qBAAA,eAAnBA,qBAAA,CAAqBJ,OAAO,GAAE;QACjC1B,KAAK,CAAC0B,OAAO,GAAG,2CAA2C;MAC7D;IACF,CAAC,MAAM,IAAI,EAAAH,gBAAA,GAAAvB,KAAK,CAACG,QAAQ,cAAAoB,gBAAA,uBAAdA,gBAAA,CAAgBnB,MAAM,MAAK,GAAG,EAAE;MAAA,IAAA2B,qBAAA;MACzC,IAAI,GAAAA,qBAAA,GAAC/B,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAA0B,qBAAA,eAAnBA,qBAAA,CAAqBL,OAAO,GAAE;QACjC1B,KAAK,CAAC0B,OAAO,GAAG,6CAA6C;MAC/D;IACF,CAAC,MAAM,IAAI,EAAAF,gBAAA,GAAAxB,KAAK,CAACG,QAAQ,cAAAqB,gBAAA,uBAAdA,gBAAA,CAAgBpB,MAAM,MAAK,GAAG,EAAE;MAAA,IAAA4B,qBAAA;MACzC;MACA,IAAI,GAAAA,qBAAA,GAAChC,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAA2B,qBAAA,eAAnBA,qBAAA,CAAqBN,OAAO,GAAE;QACjC1B,KAAK,CAAC0B,OAAO,GAAG,wDAAwD;MAC1E;IACF;EACF,CAAC,MAAM;IACL1C,OAAO,CAACgB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;EAC3C;EACA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMiC,GAAG,GAAG,MAAAA,CAAUlC,GAAW,EAAEJ,MAA2B,KAAiB;EACpF,IAAI;IACFX,OAAO,CAACC,GAAG,CAAC,0BAA0BT,UAAU,CAAC,CAAC,GAAGuB,GAAG,EAAE,CAAC;IAC3D,MAAMI,QAA0B,GAAG,MAAMjB,SAAS,CAAC+C,GAAG,CAAClC,GAAG,EAAEJ,MAAM,CAAC;IACnEX,OAAO,CAACC,GAAG,CAAC,oBAAoBc,GAAG,yBAAyB,EAAEI,QAAQ,CAACC,MAAM,CAAC;IAC9E,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;IACdhB,OAAO,CAACgB,KAAK,CAAC,4BAA4BD,GAAG,GAAG,EAAEC,KAAK,CAAC;IACxD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMkC,IAAI,GAAG,MAAAA,CAAUnC,GAAW,EAAEM,IAAU,EAAEV,MAA2B,KAAiB;EACjG,IAAI;IACFX,OAAO,CAACC,GAAG,CAAC,2BAA2BT,UAAU,CAAC,CAAC,GAAGuB,GAAG,EAAE,EAAEM,IAAI,CAAC;IAClE,MAAMF,QAA0B,GAAG,MAAMjB,SAAS,CAACgD,IAAI,CAACnC,GAAG,EAAEM,IAAI,EAAEV,MAAM,CAAC;IAC1EX,OAAO,CAACC,GAAG,CAAC,qBAAqBc,GAAG,yBAAyB,EAAEI,QAAQ,CAACC,MAAM,EAAED,QAAQ,CAACE,IAAI,CAAC;;IAE9F;IACA,IAAIF,QAAQ,CAACC,MAAM,IAAI,GAAG,IAAID,QAAQ,CAACC,MAAM,GAAG,GAAG,EAAE;MACnD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,MAAM;MACL;MACA,MAAM,IAAI8B,KAAK,CAAC,+BAA+BhC,QAAQ,CAACC,MAAM,EAAE,CAAC;IACnE;EACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdhB,OAAO,CAACgB,KAAK,CAAC,6BAA6BD,GAAG,GAAG,EAAEC,KAAK,CAAC;;IAEzD;IACA;IACA,IAAIzB,KAAK,CAACsC,YAAY,CAACb,KAAK,CAAC,EAAE;MAAA,IAAAoC,gBAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,iBAAA,EAAAC,iBAAA;MAC7B;MACA,MAAMC,YAAY,GAAG;QACnBtC,MAAM,GAAAgC,gBAAA,GAAEpC,KAAK,CAACG,QAAQ,cAAAiC,gBAAA,uBAAdA,gBAAA,CAAgBhC,MAAM;QAC9BuC,UAAU,GAAAN,gBAAA,GAAErC,KAAK,CAACG,QAAQ,cAAAkC,gBAAA,uBAAdA,gBAAA,CAAgBM,UAAU;QACtC5C,GAAG,GAAAuC,cAAA,GAAEtC,KAAK,CAACL,MAAM,cAAA2C,cAAA,uBAAZA,cAAA,CAAcvC,GAAG;QACtBF,MAAM,GAAA0C,cAAA,GAAEvC,KAAK,CAACL,MAAM,cAAA4C,cAAA,uBAAZA,cAAA,CAAc1C,MAAM;QAC5BQ,IAAI,GAAAmC,iBAAA,GAAExC,KAAK,CAACG,QAAQ,cAAAqC,iBAAA,uBAAdA,iBAAA,CAAgBnC;MACxB,CAAC;MACDrB,OAAO,CAACgB,KAAK,CAAC,sBAAsB,EAAE0C,YAAY,CAAC;MAEnD,KAAAD,iBAAA,GAAIzC,KAAK,CAACG,QAAQ,cAAAsC,iBAAA,eAAdA,iBAAA,CAAgBpC,IAAI,EAAE;QACxB,IAAI,OAAOL,KAAK,CAACG,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;UAC3CL,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI;QACrC,CAAC,MAAM,IAAIL,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO,EAAE;UACtC1B,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO;QAC7C;MACF;IACF;IACA,MAAM1B,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM4C,GAAG,GAAG,MAAAA,CAAU7C,GAAW,EAAEM,IAAU,EAAEV,MAA2B,KAAiB;EAChG,IAAI;IACFX,OAAO,CAACC,GAAG,CAAC,0BAA0BT,UAAU,CAAC,CAAC,GAAGuB,GAAG,EAAE,CAAC;IAC3D,MAAMI,QAA0B,GAAG,MAAMjB,SAAS,CAAC0D,GAAG,CAAC7C,GAAG,EAAEM,IAAI,EAAEV,MAAM,CAAC;IACzEX,OAAO,CAACC,GAAG,CAAC,kBAAkBc,GAAG,yBAAyB,EAAEI,QAAQ,CAACC,MAAM,CAAC;IAC5E,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;IAAA,IAAA6C,iBAAA;IACd7D,OAAO,CAACgB,KAAK,CAAC,0BAA0BD,GAAG,GAAG,EAAEC,KAAK,CAAC;;IAEtD;IACA;IACA,IAAIzB,KAAK,CAACsC,YAAY,CAACb,KAAK,CAAC,KAAA6C,iBAAA,GAAI7C,KAAK,CAACG,QAAQ,cAAA0C,iBAAA,eAAdA,iBAAA,CAAgBxC,IAAI,EAAE;MACrD,IAAI,OAAOL,KAAK,CAACG,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;QAC3CL,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI;MACrC,CAAC,MAAM,IAAIL,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO,EAAE;QACtC1B,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO;MAC7C;IACF;IACA,MAAM1B,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM8C,GAAG,GAAG,MAAAA,CAAU/C,GAAW,EAAEJ,MAA2B,KAAiB;EACpF,IAAI;IACFX,OAAO,CAACC,GAAG,CAAC,6BAA6BT,UAAU,CAAC,CAAC,GAAGuB,GAAG,EAAE,CAAC;IAC9D,MAAMI,QAA0B,GAAG,MAAMjB,SAAS,CAAC6D,MAAM,CAAChD,GAAG,EAAEJ,MAAM,CAAC;IACtEX,OAAO,CAACC,GAAG,CAAC,qBAAqBc,GAAG,yBAAyB,EAAEI,QAAQ,CAACC,MAAM,CAAC;IAC/E,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;IAAA,IAAAgD,iBAAA;IACdhE,OAAO,CAACgB,KAAK,CAAC,6BAA6BD,GAAG,GAAG,EAAEC,KAAK,CAAC;;IAEzD;IACA;IACA,IAAIzB,KAAK,CAACsC,YAAY,CAACb,KAAK,CAAC,KAAAgD,iBAAA,GAAIhD,KAAK,CAACG,QAAQ,cAAA6C,iBAAA,eAAdA,iBAAA,CAAgB3C,IAAI,EAAE;MACrD,IAAI,OAAOL,KAAK,CAACG,QAAQ,CAACE,IAAI,KAAK,QAAQ,EAAE;QAC3CL,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI;MACrC,CAAC,MAAM,IAAIL,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO,EAAE;QACtC1B,KAAK,CAAC0B,OAAO,GAAG1B,KAAK,CAACG,QAAQ,CAACE,IAAI,CAACqB,OAAO;MAC7C;IACF;IACA,MAAM1B,KAAK;EACb;AACF,CAAC;AAED,eAAed,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}