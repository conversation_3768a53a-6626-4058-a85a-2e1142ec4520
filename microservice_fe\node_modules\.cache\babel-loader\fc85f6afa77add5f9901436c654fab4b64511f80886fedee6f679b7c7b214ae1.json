{"ast": null, "code": "import { normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport { formatDateForInput } from '../../utils/dateUtils';\nconst BASE_URL = '/api/customer-statistics';\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(customer => {\n            // Đảm bảo customer không null\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      if (axios.isAxiosError(error)) {\n        var _error$response, _error$response2, _error$config;\n        console.error('Response data:', (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data);\n        console.error('Response status:', (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status);\n        console.error('Request URL:', (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue');\n        }\n      }\n      throw error;\n    }\n  },\n  // Get customer invoices\n  getCustomerInvoices: async (customerId, startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        customerId,\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        result = await get(`${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get invoices through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('Invoices result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        // Xử lý dữ liệu trả về để đảm bảo các trường cần thiết\n        return result.map(invoice => ({\n          ...invoice,\n          // Đảm bảo các trường quan trọng có giá trị mặc định nếu null\n          id: invoice.id || 0,\n          paymentCode: invoice.paymentCode || '',\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          contractCode: invoice.contractCode || 'Không xác định',\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          return [result];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n\n      // If result is not an array, return empty array\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      if (axios.isAxiosError(error)) {\n        var _error$response3, _error$response4, _error$config2;\n        console.error('Response data:', (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data);\n        console.error('Response status:', (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status);\n        console.error('Request URL:', (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue in getCustomerInvoices');\n        }\n      }\n      throw error;\n    }\n  },\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = date => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 12, 0, 0);\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};", "map": {"version": 3, "names": ["normalizeCustomerRevenue", "normalizeTimeBasedRevenue", "get", "formatDateForInput", "BASE_URL", "customerStatisticsService", "getCustomerRevenueStatistics", "startDate", "endDate", "isConnected", "checkApiConnection", "console", "error", "Error", "formattedStartDate", "formatDateToString", "formattedEndDate", "log", "result", "timeout", "headers", "gatewayError", "directResponse", "axios", "data", "Array", "isArray", "normalizedData", "map", "customer", "filter", "err", "warn", "singleItem", "isAxiosError", "_error$response", "_error$response2", "_error$config", "response", "status", "config", "url", "message", "includes", "getCustomerInvoices", "customerId", "invoice", "id", "paymentCode", "paymentAmount", "contractCode", "paymentDate", "Date", "_error$response3", "_error$response4", "_error$config2", "getDailyRevenueStatistics", "item", "sort", "a", "b", "dateA", "date", "dateB", "getTime", "getWeeklyRevenueStatistics", "getMonthlyRevenueStatistics", "getYearlyRevenueStatistics", "isNaN", "today", "adjustedDate", "getFullYear", "getMonth", "getDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/statistics/customerStatisticsService.ts"], "sourcesContent": ["import { CustomerRevenue, CustomerPayment, TimeBasedRevenue, normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\nconst BASE_URL = '/api/customer-statistics';\n\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate: Date, endDate: Date): Promise<CustomerRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<CustomerRevenue[]>(\n          `${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(customer => {\n            // Đảm bảo customer không null\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      if (axios.isAxiosError(error)) {\n        console.error('Response data:', error.response?.data);\n        console.error('Response status:', error.response?.status);\n        console.error('Request URL:', error.config?.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue');\n        }\n      }\n      throw error;\n    }\n  },\n\n  // Get customer invoices\n  getCustomerInvoices: async (customerId: number, startDate: Date, endDate: Date): Promise<CustomerPayment[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { customerId, startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        result = await get<CustomerPayment[]>(\n          `${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get invoices through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('Invoices result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        // Xử lý dữ liệu trả về để đảm bảo các trường cần thiết\n        return result.map(invoice => ({\n          ...invoice,\n          // Đảm bảo các trường quan trọng có giá trị mặc định nếu null\n          id: invoice.id || 0,\n          paymentCode: invoice.paymentCode || '',\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          contractCode: invoice.contractCode || 'Không xác định',\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          // Nếu là object, thử chuyển thành array\n          return [result as CustomerPayment];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n\n      // If result is not an array, return empty array\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      if (axios.isAxiosError(error)) {\n        console.error('Response data:', error.response?.data);\n        console.error('Response status:', error.response?.status);\n        console.error('Request URL:', error.config?.url);\n\n        // Kiểm tra lỗi CORS\n        if (error.message.includes('Network Error') || !error.response) {\n          console.error('Possible CORS or network issue in getCustomerInvoices');\n        }\n      }\n      throw error;\n    }\n  },\n\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = (date: Date): string => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(\n      date.getFullYear(),\n      date.getMonth(),\n      date.getDate(),\n      12, 0, 0\n    );\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};\n"], "mappings": "AAAA,SAA6DA,wBAAwB,EAAEC,yBAAyB,QAAQ,cAAc;AACtI,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,kBAAkB,QAAQ,uBAAuB;AAE1D,MAAMC,QAAQ,GAAG,0BAA0B;AAE3C,OAAO,MAAMC,yBAAyB,GAAG;EACvC;EACAC,4BAA4B,EAAE,MAAAA,CAAOC,SAAe,EAAEC,OAAa,KAAiC;IAClG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACR,SAAS,CAAC;MACxD,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACP,OAAO,CAAC;;MAEpD;MACAG,OAAO,CAACM,GAAG,CAAC,gBAAgBb,QAAQ,sBAAsBU,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAC3GL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAE;QAAEV,SAAS,EAAEO,kBAAkB;QAAEN,OAAO,EAAEQ;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIE,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMhB,GAAG,CAChB,GAAGE,QAAQ,sBAAsBU,kBAAkB,YAAYE,gBAAgB,EAAE,EACjF;UACEG,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBV,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAES,YAAY,CAAC;QACtEV,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAACrB,GAAG,CACpC,mEAAmEY,kBAAkB,YAAYE,gBAAgB,EAAE,EACnH;UACEG,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDF,MAAM,GAAGI,cAAc,CAACE,IAAI;MAC9B;MAEAb,OAAO,CAACM,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXP,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIa,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMS,cAAc,GAAGT,MAAM,CAACU,GAAG,CAACC,QAAQ,IAAI;YAC5C;YACA,IAAI,CAACA,QAAQ,EAAE,OAAO7B,wBAAwB,CAAC,IAAI,CAAC;YACpD,OAAOA,wBAAwB,CAAC6B,QAAQ,CAAC;UAC3C,CAAC,CAAC,CAACC,MAAM,CAACD,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC;UAExClB,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEU,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEmB,GAAG,CAAC;UAC1D,OAAO,EAAE;QACX;MACF;;MAEA;MACA,IAAI,OAAOb,MAAM,KAAK,QAAQ,EAAE;QAC9BP,OAAO,CAACqB,IAAI,CAAC,gDAAgD,EAAEd,MAAM,CAAC;QACtE,IAAI;UACF;UACA,MAAMe,UAAU,GAAGjC,wBAAwB,CAACkB,MAAM,CAAC;UACnD,OAAO,CAACe,UAAU,CAAC;QACrB,CAAC,CAAC,OAAOF,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEmB,GAAG,CAAC;QAC1D;MACF;MAEApB,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEM,MAAM,CAAC;MAC/C,OAAO,EAAE;IACX,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,IAAIW,KAAK,CAACW,YAAY,CAACtB,KAAK,CAAC,EAAE;QAAA,IAAAuB,eAAA,EAAAC,gBAAA,EAAAC,aAAA;QAC7B1B,OAAO,CAACC,KAAK,CAAC,gBAAgB,GAAAuB,eAAA,GAAEvB,KAAK,CAAC0B,QAAQ,cAAAH,eAAA,uBAAdA,eAAA,CAAgBX,IAAI,CAAC;QACrDb,OAAO,CAACC,KAAK,CAAC,kBAAkB,GAAAwB,gBAAA,GAAExB,KAAK,CAAC0B,QAAQ,cAAAF,gBAAA,uBAAdA,gBAAA,CAAgBG,MAAM,CAAC;QACzD5B,OAAO,CAACC,KAAK,CAAC,cAAc,GAAAyB,aAAA,GAAEzB,KAAK,CAAC4B,MAAM,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,GAAG,CAAC;;QAEhD;QACA,IAAI7B,KAAK,CAAC8B,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC/B,KAAK,CAAC0B,QAAQ,EAAE;UAC9D3B,OAAO,CAACC,KAAK,CAAC,gCAAgC,CAAC;QACjD;MACF;MACA,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAgC,mBAAmB,EAAE,MAAAA,CAAOC,UAAkB,EAAEtC,SAAe,EAAEC,OAAa,KAAiC;IAC7G,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACR,SAAS,CAAC;MACxD,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACP,OAAO,CAAC;;MAEpD;MACAG,OAAO,CAACM,GAAG,CAAC,gBAAgBb,QAAQ,aAAayC,UAAU,uBAAuB/B,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACnIL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAE;QAAE4B,UAAU;QAAEtC,SAAS,EAAEO,kBAAkB;QAAEN,OAAO,EAAEQ;MAAiB,CAAC,CAAC;;MAE5G;MACA,IAAIE,MAAM;MACV,IAAI;QACFA,MAAM,GAAG,MAAMhB,GAAG,CAChB,GAAGE,QAAQ,aAAayC,UAAU,uBAAuB/B,kBAAkB,YAAYE,gBAAgB,EAAE,EACzG;UACEG,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBV,OAAO,CAACC,KAAK,CAAC,6CAA6C,EAAES,YAAY,CAAC;QAC1EV,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAACrB,GAAG,CACpC,0DAA0D2C,UAAU,uBAAuB/B,kBAAkB,YAAYE,gBAAgB,EAAE,EAC3I;UACEG,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDF,MAAM,GAAGI,cAAc,CAACE,IAAI;MAC9B;MAEAb,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEC,MAAM,CAAC;;MAEvC;MACA,IAAI,CAACA,MAAM,EAAE;QACXP,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACnE,OAAO,EAAE;MACX;;MAEA;MACA,IAAIa,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;QACzB;QACA,OAAOA,MAAM,CAACU,GAAG,CAACkB,OAAO,KAAK;UAC5B,GAAGA,OAAO;UACV;UACAC,EAAE,EAAED,OAAO,CAACC,EAAE,IAAI,CAAC;UACnBC,WAAW,EAAEF,OAAO,CAACE,WAAW,IAAI,EAAE;UACtCC,aAAa,EAAE,OAAOH,OAAO,CAACG,aAAa,KAAK,QAAQ,GAAGH,OAAO,CAACG,aAAa,GAAG,CAAC;UACpFC,YAAY,EAAEJ,OAAO,CAACI,YAAY,IAAI,gBAAgB;UACtDC,WAAW,EAAEL,OAAO,CAACK,WAAW,GAAG,IAAIC,IAAI,CAACN,OAAO,CAACK,WAAW,CAAC,GAAG,IAAIC,IAAI,CAAC;QAC9E,CAAC,CAAC,CAAC,CAACtB,MAAM,CAACgB,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC;MACzC;;MAEA;MACA,IAAI,OAAO5B,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;QACjDP,OAAO,CAACqB,IAAI,CAAC,yDAAyD,EAAEd,MAAM,CAAC;QAC/E,IAAI;UACF;UACA,OAAO,CAACA,MAAM,CAAoB;QACpC,CAAC,CAAC,OAAOa,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,4CAA4C,EAAEmB,GAAG,CAAC;QAClE;MACF;;MAEA;MACApB,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEM,MAAM,CAAC;MACxD,OAAO,EAAE;IACX,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAIW,KAAK,CAACW,YAAY,CAACtB,KAAK,CAAC,EAAE;QAAA,IAAAyC,gBAAA,EAAAC,gBAAA,EAAAC,cAAA;QAC7B5C,OAAO,CAACC,KAAK,CAAC,gBAAgB,GAAAyC,gBAAA,GAAEzC,KAAK,CAAC0B,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgB7B,IAAI,CAAC;QACrDb,OAAO,CAACC,KAAK,CAAC,kBAAkB,GAAA0C,gBAAA,GAAE1C,KAAK,CAAC0B,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBf,MAAM,CAAC;QACzD5B,OAAO,CAACC,KAAK,CAAC,cAAc,GAAA2C,cAAA,GAAE3C,KAAK,CAAC4B,MAAM,cAAAe,cAAA,uBAAZA,cAAA,CAAcd,GAAG,CAAC;;QAEhD;QACA,IAAI7B,KAAK,CAAC8B,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC/B,KAAK,CAAC0B,QAAQ,EAAE;UAC9D3B,OAAO,CAACC,KAAK,CAAC,uDAAuD,CAAC;QACxE;MACF;MACA,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACA4C,yBAAyB,EAAE,MAAAA,CAAOjD,SAAe,EAAEC,OAAa,KAAkC;IAChG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACR,SAAS,CAAC;MACxD,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACP,OAAO,CAAC;;MAEpD;MACAG,OAAO,CAACM,GAAG,CAAC,gBAAgBb,QAAQ,4BAA4BU,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACjHL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAE;QAAEV,SAAS,EAAEO,kBAAkB;QAAEN,OAAO,EAAEQ;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIE,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMhB,GAAG,CAChB,GAAGE,QAAQ,4BAA4BU,kBAAkB,YAAYE,gBAAgB,EAAE,EACvF;UACEG,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBV,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAES,YAAY,CAAC;QACtEV,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAACrB,GAAG,CACpC,yEAAyEY,kBAAkB,YAAYE,gBAAgB,EAAE,EACzH;UACEG,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDF,MAAM,GAAGI,cAAc,CAACE,IAAI;MAC9B;MAEAb,OAAO,CAACM,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXP,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIa,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMS,cAAc,GAAGT,MAAM,CAACU,GAAG,CAAC6B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOxD,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACwD,IAAI,CAAC;UACxC,CAAC,CAAC,CACD3B,MAAM,CAAC2B,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFrD,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEU,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEmB,GAAG,CAAC;UACvD,OAAO,EAAE;QACX;MACF;;MAEA;MACApB,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEM,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAqD,0BAA0B,EAAE,MAAAA,CAAO1D,SAAe,EAAEC,OAAa,KAAkC;IACjG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACR,SAAS,CAAC;MACxD,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACP,OAAO,CAAC;;MAEpD;MACAG,OAAO,CAACM,GAAG,CAAC,gBAAgBb,QAAQ,6BAA6BU,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAClHL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAE;QAAEV,SAAS,EAAEO,kBAAkB;QAAEN,OAAO,EAAEQ;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIE,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMhB,GAAG,CAChB,GAAGE,QAAQ,6BAA6BU,kBAAkB,YAAYE,gBAAgB,EAAE,EACxF;UACEG,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBV,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAES,YAAY,CAAC;QACtEV,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAACrB,GAAG,CACpC,0EAA0EY,kBAAkB,YAAYE,gBAAgB,EAAE,EAC1H;UACEG,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDF,MAAM,GAAGI,cAAc,CAACE,IAAI;MAC9B;MAEAb,OAAO,CAACM,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXP,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIa,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMS,cAAc,GAAGT,MAAM,CAACU,GAAG,CAAC6B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOxD,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACwD,IAAI,CAAC;UACxC,CAAC,CAAC,CAAC3B,MAAM,CAAC2B,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;UAEhC9C,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEU,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEmB,GAAG,CAAC;UACxD,OAAO,EAAE;QACX;MACF;;MAEA;MACApB,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEM,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAsD,2BAA2B,EAAE,MAAAA,CAAO3D,SAAe,EAAEC,OAAa,KAAkC;IAClG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACR,SAAS,CAAC;MACxD,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACP,OAAO,CAAC;;MAEpD;MACAG,OAAO,CAACM,GAAG,CAAC,gBAAgBb,QAAQ,8BAA8BU,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACnHL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAE;QAAEV,SAAS,EAAEO,kBAAkB;QAAEN,OAAO,EAAEQ;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIE,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMhB,GAAG,CAChB,GAAGE,QAAQ,8BAA8BU,kBAAkB,YAAYE,gBAAgB,EAAE,EACzF;UACEG,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBV,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAES,YAAY,CAAC;QACtEV,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAACrB,GAAG,CACpC,2EAA2EY,kBAAkB,YAAYE,gBAAgB,EAAE,EAC3H;UACEG,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDF,MAAM,GAAGI,cAAc,CAACE,IAAI;MAC9B;MAEAb,OAAO,CAACM,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXP,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIa,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMS,cAAc,GAAGT,MAAM,CAACU,GAAG,CAAC6B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOxD,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACwD,IAAI,CAAC;UACxC,CAAC,CAAC,CACD3B,MAAM,CAAC2B,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFrD,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEU,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEmB,GAAG,CAAC;UACzD,OAAO,EAAE;QACX;MACF;;MAEA;MACApB,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEM,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAuD,0BAA0B,EAAE,MAAAA,CAAO5D,SAAe,EAAEC,OAAa,KAAkC;IACjG,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIC,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACR,SAAS,CAAC;MACxD,MAAMS,gBAAgB,GAAGD,kBAAkB,CAACP,OAAO,CAAC;;MAEpD;MACAG,OAAO,CAACM,GAAG,CAAC,gBAAgBb,QAAQ,6BAA6BU,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAClHL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAE;QAAEV,SAAS,EAAEO,kBAAkB;QAAEN,OAAO,EAAEQ;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIE,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMhB,GAAG,CAChB,GAAGE,QAAQ,6BAA6BU,kBAAkB,YAAYE,gBAAgB,EAAE,EACxF;UACEG,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrBV,OAAO,CAACC,KAAK,CAAC,yCAAyC,EAAES,YAAY,CAAC;QACtEV,OAAO,CAACM,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMK,cAAc,GAAG,MAAMC,KAAK,CAACrB,GAAG,CACpC,0EAA0EY,kBAAkB,YAAYE,gBAAgB,EAAE,EAC1H;UACEG,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDF,MAAM,GAAGI,cAAc,CAACE,IAAI;MAC9B;MAEAb,OAAO,CAACM,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXP,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIa,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMS,cAAc,GAAGT,MAAM,CAACU,GAAG,CAAC6B,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOxD,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACwD,IAAI,CAAC;UACxC,CAAC,CAAC,CACD3B,MAAM,CAAC2B,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIT,IAAI,CAACO,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAACQ,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFrD,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEU,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZpB,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEmB,GAAG,CAAC;UACxD,OAAO,EAAE;QACX;MACF;;MAEA;MACApB,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEM,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,MAAMG,kBAAkB,GAAI+C,IAAU,IAAa;EACjD,IAAI;IACF;IACA,IAAI,EAAEA,IAAI,YAAYV,IAAI,CAAC,IAAIgB,KAAK,CAACN,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MACpDrD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEkD,IAAI,CAAC;MAC7C;MACA,MAAMO,KAAK,GAAG,IAAIjB,IAAI,CAAC,CAAC;MACxB,OAAOjD,kBAAkB,CAACkE,KAAK,CAAC;IAClC;;IAEA;IACA;IACA,MAAMC,YAAY,GAAG,IAAIlB,IAAI,CAC3BU,IAAI,CAACS,WAAW,CAAC,CAAC,EAClBT,IAAI,CAACU,QAAQ,CAAC,CAAC,EACfV,IAAI,CAACW,OAAO,CAAC,CAAC,EACd,EAAE,EAAE,CAAC,EAAE,CACT,CAAC;;IAED;IACA,OAAOtE,kBAAkB,CAACmE,YAAY,CAAC;EACzC,CAAC,CAAC,OAAO1D,KAAK,EAAE;IACdD,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C;IACA,MAAMyD,KAAK,GAAG,IAAIjB,IAAI,CAAC,CAAC;IACxB,OAAOjD,kBAAkB,CAACkE,KAAK,CAAC;EAClC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}