{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Box,TextField,Button,Typography,Paper,Divider,Stepper,Step,StepLabel,Card,CardContent,IconButton,Tooltip,useTheme}from'@mui/material';import AddIcon from'@mui/icons-material/Add';import PersonIcon from'@mui/icons-material/Person';import DateRangeIcon from'@mui/icons-material/DateRange';import DescriptionIcon from'@mui/icons-material/Description';import MonetizationOnIcon from'@mui/icons-material/MonetizationOn';import HelpOutlineIcon from'@mui/icons-material/HelpOutline';import JobDetailForm from'./JobDetailForm';import ContractAmountCalculation from'./ContractAmountCalculation';import{<PERSON><PERSON><PERSON><PERSON>,DatePicker<PERSON>ield}from'../common';import{CustomerDialog}from'../customer';import{calculateContractAmount,calculateContractDates}from'../../utils/contractCalculationUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CustomerContractForm=_ref=>{var _contract$jobDetails2;let{contract,onChange,onSubmit,isEdit=false,loading=false}=_ref;const[customerDialogOpen,setCustomerDialogOpen]=useState(false);const theme=useTheme();const handleInputChange=e=>{const{name,value}=e.target;const updatedContract=_objectSpread(_objectSpread({},contract),{},{[name]:value});// Auto-calculate total amount when dates change\nif(name==='startingDate'||name==='endingDate'){const calculation=calculateContractAmount(updatedContract);updatedContract.totalAmount=calculation.totalAmount;}onChange(updatedContract);};const handleFormSubmit=e=>{e.preventDefault();if(!loading){var _contract$jobDetails;console.log('🚀 Form submission triggered:',{customerId:contract.customerId,jobDetailsCount:((_contract$jobDetails=contract.jobDetails)===null||_contract$jobDetails===void 0?void 0:_contract$jobDetails.length)||0,totalAmount:contract.totalAmount});onSubmit();}};const handleOpenCustomerDialog=()=>{setCustomerDialogOpen(true);};const handleCloseCustomerDialog=()=>{setCustomerDialogOpen(false);};const handleSelectCustomer=customer=>{onChange(_objectSpread(_objectSpread({},contract),{},{customerId:customer.id,customerName:customer.fullName}));};const handleJobDetailChange=(index,jobDetail)=>{const updatedJobDetails=[...(contract.jobDetails||[])];updatedJobDetails[index]=jobDetail;const updatedContract=_objectSpread(_objectSpread({},contract),{},{jobDetails:updatedJobDetails});// Auto-calculate contract dates from job details\nconst contractDates=calculateContractDates(updatedContract);if(contractDates.startingDate&&contractDates.endingDate){updatedContract.startingDate=contractDates.startingDate;updatedContract.endingDate=contractDates.endingDate;}// Auto-calculate total amount when job details change\nconst calculation=calculateContractAmount(updatedContract);updatedContract.totalAmount=calculation.totalAmount;onChange(updatedContract);};const handleAddJobDetail=()=>{const newJobDetail={jobCategoryId:0,startDate:'',endDate:'',workLocation:'',workShifts:[]};onChange(_objectSpread(_objectSpread({},contract),{},{jobDetails:[...(contract.jobDetails||[]),newJobDetail]}));};const handleDeleteJobDetail=index=>{const updatedJobDetails=[...(contract.jobDetails||[])];updatedJobDetails.splice(index,1);onChange(_objectSpread(_objectSpread({},contract),{},{jobDetails:updatedJobDetails}));};return/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleFormSubmit,children:[/*#__PURE__*/_jsx(PageHeader,{title:isEdit?\"Chỉnh sửa Hợp đồng\":\"Tạo Hợp đồng Mới\",subtitle:\"Nh\\u1EADp th\\xF4ng tin h\\u1EE3p \\u0111\\u1ED3ng b\\xEAn d\\u01B0\\u1EDBi\"}),/*#__PURE__*/_jsxs(Stepper,{activeStep:contract.customerId?(_contract$jobDetails2=contract.jobDetails)!==null&&_contract$jobDetails2!==void 0&&_contract$jobDetails2.length?2:1:0,alternativeLabel:true,sx:{mb:4},children:[/*#__PURE__*/_jsx(Step,{children:/*#__PURE__*/_jsx(StepLabel,{children:\"Ch\\u1ECDn kh\\xE1ch h\\xE0ng\"})}),/*#__PURE__*/_jsx(Step,{children:/*#__PURE__*/_jsx(StepLabel,{children:\"Th\\xF4ng tin h\\u1EE3p \\u0111\\u1ED3ng\"})}),/*#__PURE__*/_jsx(Step,{children:/*#__PURE__*/_jsx(StepLabel,{children:\"Chi ti\\u1EBFt c\\xF4ng vi\\u1EC7c\"})})]}),/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:3,mb:4,border:'1px solid #e0e0e0',borderRadius:'8px',background:theme.palette.background.paper,position:'relative',overflow:'hidden','&::before':{content:'\"\"',position:'absolute',top:0,left:0,width:'100%',height:'8px',background:theme.palette.primary.main}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",sx:{mb:3,color:theme.palette.primary.main,fontWeight:'bold'},children:\"H\\u1EE2P \\u0110\\u1ED2NG CUNG C\\u1EA4P D\\u1ECACH V\\u1EE4 NH\\xC2N C\\xD4NG\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:3},children:[/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'48%'}},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2,height:'100%'},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",sx:{mb:2,fontWeight:'bold',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(PersonIcon,{sx:{mr:1}}),\"Th\\xF4ng tin kh\\xE1ch h\\xE0ng\"]}),contract.customerId?/*#__PURE__*/_jsxs(Box,{sx:{p:2,border:'1px dashed #ccc',borderRadius:'4px',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold'},children:contract.customerName}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",onClick:handleOpenCustomerDialog,sx:{mt:1},children:\"Thay \\u0111\\u1ED5i\"})]}):/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",fullWidth:true,onClick:handleOpenCustomerDialog,sx:{height:56},startIcon:/*#__PURE__*/_jsx(PersonIcon,{}),children:\"Ch\\u1ECDn kh\\xE1ch h\\xE0ng\"})]})})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'48%'}},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",sx:{mb:2,fontWeight:'bold',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(DateRangeIcon,{sx:{mr:1}}),\"Th\\u1EDDi gian hi\\u1EC7u l\\u1EF1c h\\u1EE3p \\u0111\\u1ED3ng (T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh to\\xE1n)\",/*#__PURE__*/_jsx(Tooltip,{title:\"Th\\u1EDDi gian h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u01B0\\u1EE3c t\\xEDnh t\\u1EF1 \\u0111\\u1ED9ng t\\u1EEB ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u s\\u1EDBm nh\\u1EA5t v\\xE0 ng\\xE0y k\\u1EBFt th\\xFAc mu\\u1ED9n nh\\u1EA5t c\\u1EE7a c\\xE1c c\\xF4ng vi\\u1EC7c\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{ml:1},children:/*#__PURE__*/_jsx(HelpOutlineIcon,{fontSize:\"small\"})})})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',sm:'48%'}},children:/*#__PURE__*/_jsx(DatePickerField,{label:\"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u (T\\u1EF1 \\u0111\\u1ED9ng)\",value:contract.startingDate||'',onChange:()=>{}// Read-only\n,required:true,disabled:true})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',sm:'48%'}},children:/*#__PURE__*/_jsx(DatePickerField,{label:\"Ng\\xE0y k\\u1EBFt th\\xFAc (T\\u1EF1 \\u0111\\u1ED9ng)\",value:contract.endingDate||'',onChange:()=>{}// Read-only\n,required:true,disabled:true})})]})]})})}),/*#__PURE__*/_jsx(Box,{sx:{width:{xs:'100%',md:'48%'}},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",sx:{mb:2,fontWeight:'bold',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(MonetizationOnIcon,{sx:{mr:1}}),\"Gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng (T\\u1EF1 \\u0111\\u1ED9ng t\\xEDnh to\\xE1n)\",/*#__PURE__*/_jsx(Tooltip,{title:\"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u01B0\\u1EE3c t\\xEDnh t\\u1EF1 \\u0111\\u1ED9ng d\\u1EF1a tr\\xEAn l\\u01B0\\u01A1ng, s\\u1ED1 ng\\u01B0\\u1EDDi v\\xE0 s\\u1ED1 ng\\xE0y l\\xE0m vi\\u1EC7c\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{ml:1},children:/*#__PURE__*/_jsx(HelpOutlineIcon,{fontSize:\"small\"})})})]}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"T\\u1ED5ng gi\\xE1 tr\\u1ECB h\\u1EE3p \\u0111\\u1ED3ng (VN\\u0110)\",name:\"totalAmount\",type:\"text\",value:contract.totalAmount?contract.totalAmount.toLocaleString('vi-VN')+' VNĐ':'0 VNĐ',slotProps:{input:{readOnly:true}},sx:{'& input':{fontWeight:'bold',color:theme.palette.success.main,backgroundColor:theme.palette.action.hover}}})]})})}),/*#__PURE__*/_jsx(Box,{sx:{width:'100%'},children:/*#__PURE__*/_jsx(Card,{variant:\"outlined\",sx:{mb:2},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",sx:{mb:2,fontWeight:'bold',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(DescriptionIcon,{sx:{mr:1}}),\"M\\xF4 t\\u1EA3 h\\u1EE3p \\u0111\\u1ED3ng\"]}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"M\\xF4 t\\u1EA3 chi ti\\u1EBFt v\\u1EC1 h\\u1EE3p \\u0111\\u1ED3ng\",name:\"description\",value:contract.description||'',onChange:handleInputChange,onKeyDown:e=>{// Prevent Enter from submitting in multiline text field\nif(e.key==='Enter'&&!e.shiftKey){e.stopPropagation();}},multiline:true,rows:3,placeholder:\"Nh\\u1EADp c\\xE1c th\\xF4ng tin b\\u1ED5 sung v\\u1EC1 h\\u1EE3p \\u0111\\u1ED3ng (kh\\xF4ng b\\u1EAFt bu\\u1ED9c)\"})]})})})]}),/*#__PURE__*/_jsx(CustomerDialog,{open:customerDialogOpen,onClose:handleCloseCustomerDialog,onSelectCustomer:handleSelectCustomer})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",sx:{mb:2,mt:4,fontWeight:'bold',color:theme.palette.primary.main},children:\"CHI TI\\u1EBET C\\xD4NG VI\\u1EC6C\"}),(contract.jobDetails||[]).map((jobDetail,index)=>/*#__PURE__*/_jsx(JobDetailForm,{jobDetail:jobDetail,onChange:updatedJobDetail=>handleJobDetailChange(index,updatedJobDetail),onDelete:()=>handleDeleteJobDetail(index),showDelete:true},index)),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:handleAddJobDetail,sx:{mb:3},children:\"Th\\xEAm c\\xF4ng vi\\u1EC7c\"}),/*#__PURE__*/_jsx(ContractAmountCalculation,{contract:contract}),/*#__PURE__*/_jsx(Divider,{sx:{my:3}}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-end',mt:2},children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",color:\"primary\",size:\"large\",disabled:loading||!contract.customerId,children:loading?'Đang xử lý...':isEdit?'Cập nhật Hợp đồng':'Tạo Hợp đồng'})})]});};export default CustomerContractForm;", "map": {"version": 3, "names": ["React", "useState", "Box", "TextField", "<PERSON><PERSON>", "Typography", "Paper", "Divider", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "useTheme", "AddIcon", "PersonIcon", "DateRangeIcon", "DescriptionIcon", "MonetizationOnIcon", "HelpOutlineIcon", "JobDetailForm", "ContractAmountCalculation", "<PERSON><PERSON><PERSON><PERSON>", "DatePickerField", "CustomerDialog", "calculateContractAmount", "calculateContractDates", "jsx", "_jsx", "jsxs", "_jsxs", "CustomerContractForm", "_ref", "_contract$jobDetails2", "contract", "onChange", "onSubmit", "isEdit", "loading", "customerDialogOpen", "setCustomerDialogOpen", "theme", "handleInputChange", "e", "name", "value", "target", "updatedContract", "_objectSpread", "calculation", "totalAmount", "handleFormSubmit", "preventDefault", "_contract$jobDetails", "console", "log", "customerId", "jobDetailsCount", "jobDetails", "length", "handleOpenCustomerDialog", "handleCloseCustomerDialog", "handleSelectCustomer", "customer", "id", "customerName", "fullName", "handleJobDetailChange", "index", "jobDetail", "updatedJobDetails", "contractDates", "startingDate", "endingDate", "handleAddJobDetail", "newJobDetail", "jobCategoryId", "startDate", "endDate", "workLocation", "workShifts", "handleDeleteJobDetail", "splice", "component", "children", "title", "subtitle", "activeStep", "alternativeLabel", "sx", "mb", "elevation", "p", "border", "borderRadius", "background", "palette", "paper", "position", "overflow", "content", "top", "left", "width", "height", "primary", "main", "variant", "color", "fontWeight", "display", "flexWrap", "gap", "xs", "md", "alignItems", "mr", "size", "onClick", "mt", "fullWidth", "startIcon", "ml", "fontSize", "sm", "label", "required", "disabled", "type", "toLocaleString", "slotProps", "input", "readOnly", "success", "backgroundColor", "action", "hover", "description", "onKeyDown", "key", "shift<PERSON>ey", "stopPropagation", "multiline", "rows", "placeholder", "open", "onClose", "onSelectCustomer", "map", "updatedJobDetail", "onDelete", "showDelete", "my", "justifyContent"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/components/contract/CustomerContractForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Typography,\n  Paper,\n  Divider,\n  <PERSON>per,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  Card,\n  CardContent,\n  IconButton,\n  Tooltip,\n  useTheme,\n} from '@mui/material';\nimport AddIcon from '@mui/icons-material/Add';\nimport PersonIcon from '@mui/icons-material/Person';\nimport DateRangeIcon from '@mui/icons-material/DateRange';\nimport DescriptionIcon from '@mui/icons-material/Description';\nimport MonetizationOnIcon from '@mui/icons-material/MonetizationOn';\nimport HelpOutlineIcon from '@mui/icons-material/HelpOutline';\nimport { CustomerContract, Customer, JobDetail } from '../../models';\nimport JobDetailForm from './JobDetailForm';\nimport ContractAmountCalculation from './ContractAmountCalculation';\nimport { PageHeader, DatePickerField } from '../common';\nimport { CustomerDialog } from '../customer';\nimport { calculateContractAmount, calculateContractDates } from '../../utils/contractCalculationUtils';\n\ninterface CustomerContractFormProps {\n  contract: Partial<CustomerContract>;\n  onChange: (contract: Partial<CustomerContract>) => void;\n  onSubmit: () => void;\n  isEdit?: boolean;\n  loading?: boolean;\n}\n\nconst CustomerContractForm: React.FC<CustomerContractFormProps> = ({\n  contract,\n  onChange,\n  onSubmit,\n  isEdit = false,\n  loading = false,\n}) => {\n  const [customerDialogOpen, setCustomerDialogOpen] = useState(false);\n  const theme = useTheme();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    const updatedContract = {\n      ...contract,\n      [name]: value,\n    };\n\n    // Auto-calculate total amount when dates change\n    if (name === 'startingDate' || name === 'endingDate') {\n      const calculation = calculateContractAmount(updatedContract);\n      updatedContract.totalAmount = calculation.totalAmount;\n    }\n\n    onChange(updatedContract);\n  };\n\n  const handleFormSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!loading) {\n      console.log('🚀 Form submission triggered:', {\n        customerId: contract.customerId,\n        jobDetailsCount: contract.jobDetails?.length || 0,\n        totalAmount: contract.totalAmount\n      });\n      onSubmit();\n    }\n  };\n\n  const handleOpenCustomerDialog = () => {\n    setCustomerDialogOpen(true);\n  };\n\n  const handleCloseCustomerDialog = () => {\n    setCustomerDialogOpen(false);\n  };\n\n  const handleSelectCustomer = (customer: Customer) => {\n    onChange({\n      ...contract,\n      customerId: customer.id,\n      customerName: customer.fullName,\n    });\n  };\n\n  const handleJobDetailChange = (index: number, jobDetail: Partial<JobDetail>) => {\n    const updatedJobDetails = [...(contract.jobDetails || [])];\n    updatedJobDetails[index] = jobDetail as JobDetail;\n\n    const updatedContract = {\n      ...contract,\n      jobDetails: updatedJobDetails,\n    };\n\n    // Auto-calculate contract dates from job details\n    const contractDates = calculateContractDates(updatedContract);\n    if (contractDates.startingDate && contractDates.endingDate) {\n      updatedContract.startingDate = contractDates.startingDate;\n      updatedContract.endingDate = contractDates.endingDate;\n    }\n\n    // Auto-calculate total amount when job details change\n    const calculation = calculateContractAmount(updatedContract);\n    updatedContract.totalAmount = calculation.totalAmount;\n\n    onChange(updatedContract);\n  };\n\n  const handleAddJobDetail = () => {\n    const newJobDetail: JobDetail = {\n      jobCategoryId: 0,\n      startDate: '',\n      endDate: '',\n      workLocation: '',\n      workShifts: [],\n    };\n\n    onChange({\n      ...contract,\n      jobDetails: [...(contract.jobDetails || []), newJobDetail],\n    });\n  };\n\n  const handleDeleteJobDetail = (index: number) => {\n    const updatedJobDetails = [...(contract.jobDetails || [])];\n    updatedJobDetails.splice(index, 1);\n\n    onChange({\n      ...contract,\n      jobDetails: updatedJobDetails,\n    });\n  };\n\n  return (\n    <Box component=\"form\" onSubmit={handleFormSubmit}>\n      <PageHeader\n        title={isEdit ? \"Chỉnh sửa Hợp đồng\" : \"Tạo Hợp đồng Mới\"}\n        subtitle=\"Nhập thông tin hợp đồng bên dưới\"\n      />\n\n      {/* Contract workflow steps */}\n      <Stepper\n        activeStep={contract.customerId ? (contract.jobDetails?.length ? 2 : 1) : 0}\n        alternativeLabel\n        sx={{ mb: 4 }}\n      >\n        <Step>\n          <StepLabel>Chọn khách hàng</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Thông tin hợp đồng</StepLabel>\n        </Step>\n        <Step>\n          <StepLabel>Chi tiết công việc</StepLabel>\n        </Step>\n      </Stepper>\n\n      {/* Contract header with customer selection */}\n      <Paper\n        elevation={3}\n        sx={{\n          p: 3,\n          mb: 4,\n          border: '1px solid #e0e0e0',\n          borderRadius: '8px',\n          background: theme.palette.background.paper,\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '8px',\n            background: theme.palette.primary.main,\n          }\n        }}\n      >\n        <Typography variant=\"h5\" sx={{ mb: 3, color: theme.palette.primary.main, fontWeight: 'bold' }}>\n          HỢP ĐỒNG CUNG CẤP DỊCH VỤ NHÂN CÔNG\n        </Typography>\n\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n          {/* Customer selection section */}\n          <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n            <Card variant=\"outlined\" sx={{ mb: 2, height: '100%' }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <PersonIcon sx={{ mr: 1 }} />\n                  Thông tin khách hàng\n                </Typography>\n\n                {contract.customerId ? (\n                  <Box sx={{ p: 2, border: '1px dashed #ccc', borderRadius: '4px', mb: 2 }}>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 'bold' }}>\n                      {contract.customerName}\n                    </Typography>\n                    <Button\n                      variant=\"outlined\"\n                      size=\"small\"\n                      onClick={handleOpenCustomerDialog}\n                      sx={{ mt: 1 }}\n                    >\n                      Thay đổi\n                    </Button>\n                  </Box>\n                ) : (\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    fullWidth\n                    onClick={handleOpenCustomerDialog}\n                    sx={{ height: 56 }}\n                    startIcon={<PersonIcon />}\n                  >\n                    Chọn khách hàng\n                  </Button>\n                )}\n              </CardContent>\n            </Card>\n          </Box>\n\n\n\n          {/* Contract dates */}\n          <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <DateRangeIcon sx={{ mr: 1 }} />\n                  Thời gian hiệu lực hợp đồng (Tự động tính toán)\n                  <Tooltip title=\"Thời gian hợp đồng được tính tự động từ ngày bắt đầu sớm nhất và ngày kết thúc muộn nhất của các công việc\">\n                    <IconButton size=\"small\" sx={{ ml: 1 }}>\n                      <HelpOutlineIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Tooltip>\n                </Typography>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                  <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                    <DatePickerField\n                      label=\"Ngày bắt đầu (Tự động)\"\n                      value={contract.startingDate || ''}\n                      onChange={() => {}} // Read-only\n                      required\n                      disabled\n                    />\n                  </Box>\n                  <Box sx={{ width: { xs: '100%', sm: '48%' } }}>\n                    <DatePickerField\n                      label=\"Ngày kết thúc (Tự động)\"\n                      value={contract.endingDate || ''}\n                      onChange={() => {}} // Read-only\n                      required\n                      disabled\n                    />\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n          </Box>\n\n          {/* Contract value - Auto calculated */}\n          <Box sx={{ width: { xs: '100%', md: '48%' } }}>\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <MonetizationOnIcon sx={{ mr: 1 }} />\n                  Giá trị hợp đồng (Tự động tính toán)\n                  <Tooltip title=\"Tổng giá trị hợp đồng được tính tự động dựa trên lương, số người và số ngày làm việc\">\n                    <IconButton size=\"small\" sx={{ ml: 1 }}>\n                      <HelpOutlineIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Tooltip>\n                </Typography>\n                <TextField\n                  fullWidth\n                  label=\"Tổng giá trị hợp đồng (VNĐ)\"\n                  name=\"totalAmount\"\n                  type=\"text\"\n                  value={contract.totalAmount ? contract.totalAmount.toLocaleString('vi-VN') + ' VNĐ' : '0 VNĐ'}\n                  slotProps={{\n                    input: {\n                      readOnly: true,\n                    },\n                  }}\n                  sx={{\n                    '& input': {\n                      fontWeight: 'bold',\n                      color: theme.palette.success.main,\n                      backgroundColor: theme.palette.action.hover\n                    }\n                  }}\n                />\n              </CardContent>\n            </Card>\n          </Box>\n\n          {/* Description */}\n          <Box sx={{ width: '100%' }}>\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\n              <CardContent>\n                <Typography variant=\"subtitle1\" sx={{ mb: 2, fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>\n                  <DescriptionIcon sx={{ mr: 1 }} />\n                  Mô tả hợp đồng\n                </Typography>\n                <TextField\n                  fullWidth\n                  label=\"Mô tả chi tiết về hợp đồng\"\n                  name=\"description\"\n                  value={contract.description || ''}\n                  onChange={handleInputChange}\n                  onKeyDown={(e) => {\n                    // Prevent Enter from submitting in multiline text field\n                    if (e.key === 'Enter' && !e.shiftKey) {\n                      e.stopPropagation();\n                    }\n                  }}\n                  multiline\n                  rows={3}\n                  placeholder=\"Nhập các thông tin bổ sung về hợp đồng (không bắt buộc)\"\n                />\n              </CardContent>\n            </Card>\n          </Box>\n        </Box>\n\n        <CustomerDialog\n          open={customerDialogOpen}\n          onClose={handleCloseCustomerDialog}\n          onSelectCustomer={handleSelectCustomer}\n        />\n      </Paper>\n\n      {/* Job details section */}\n      <Typography variant=\"h5\" sx={{ mb: 2, mt: 4, fontWeight: 'bold', color: theme.palette.primary.main }}>\n        CHI TIẾT CÔNG VIỆC\n      </Typography>\n\n      {(contract.jobDetails || []).map((jobDetail, index) => (\n        <JobDetailForm\n          key={index}\n          jobDetail={jobDetail}\n          onChange={(updatedJobDetail) => handleJobDetailChange(index, updatedJobDetail)}\n          onDelete={() => handleDeleteJobDetail(index)}\n          showDelete={true}\n        />\n      ))}\n\n      <Button\n        variant=\"outlined\"\n        startIcon={<AddIcon />}\n        onClick={handleAddJobDetail}\n        sx={{ mb: 3 }}\n      >\n        Thêm công việc\n      </Button>\n\n      {/* Contract Amount Calculation */}\n      <ContractAmountCalculation contract={contract} />\n\n      <Divider sx={{ my: 3 }} />\n\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>\n        <Button\n          type=\"submit\"\n          variant=\"contained\"\n          color=\"primary\"\n          size=\"large\"\n          disabled={loading || !contract.customerId}\n        >\n          {loading ? 'Đang xử lý...' : (isEdit ? 'Cập nhật Hợp đồng' : 'Tạo Hợp đồng')}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default CustomerContractForm;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,SAAS,CACTC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,IAAI,CACJC,SAAS,CACTC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,OAAO,CACPC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CACnE,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAE7D,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,MAAO,CAAAC,yBAAyB,KAAM,6BAA6B,CACnE,OAASC,UAAU,CAAEC,eAAe,KAAQ,WAAW,CACvD,OAASC,cAAc,KAAQ,aAAa,CAC5C,OAASC,uBAAuB,CAAEC,sBAAsB,KAAQ,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUvG,KAAM,CAAAC,oBAAyD,CAAGC,IAAA,EAM5D,KAAAC,qBAAA,IAN6D,CACjEC,QAAQ,CACRC,QAAQ,CACRC,QAAQ,CACRC,MAAM,CAAG,KAAK,CACdC,OAAO,CAAG,KACZ,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAA0C,KAAK,CAAG5B,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAA6B,iBAAiB,CAAIC,CAAsC,EAAK,CACpE,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChC,KAAM,CAAAC,eAAe,CAAAC,aAAA,CAAAA,aAAA,IAChBd,QAAQ,MACX,CAACU,IAAI,EAAGC,KAAK,EACd,CAED;AACA,GAAID,IAAI,GAAK,cAAc,EAAIA,IAAI,GAAK,YAAY,CAAE,CACpD,KAAM,CAAAK,WAAW,CAAGxB,uBAAuB,CAACsB,eAAe,CAAC,CAC5DA,eAAe,CAACG,WAAW,CAAGD,WAAW,CAACC,WAAW,CACvD,CAEAf,QAAQ,CAACY,eAAe,CAAC,CAC3B,CAAC,CAED,KAAM,CAAAI,gBAAgB,CAAIR,CAAkB,EAAK,CAC/CA,CAAC,CAACS,cAAc,CAAC,CAAC,CAClB,GAAI,CAACd,OAAO,CAAE,KAAAe,oBAAA,CACZC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAE,CAC3CC,UAAU,CAAEtB,QAAQ,CAACsB,UAAU,CAC/BC,eAAe,CAAE,EAAAJ,oBAAA,CAAAnB,QAAQ,CAACwB,UAAU,UAAAL,oBAAA,iBAAnBA,oBAAA,CAAqBM,MAAM,GAAI,CAAC,CACjDT,WAAW,CAAEhB,QAAQ,CAACgB,WACxB,CAAC,CAAC,CACFd,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAED,KAAM,CAAAwB,wBAAwB,CAAGA,CAAA,GAAM,CACrCpB,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAqB,yBAAyB,CAAGA,CAAA,GAAM,CACtCrB,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAsB,oBAAoB,CAAIC,QAAkB,EAAK,CACnD5B,QAAQ,CAAAa,aAAA,CAAAA,aAAA,IACHd,QAAQ,MACXsB,UAAU,CAAEO,QAAQ,CAACC,EAAE,CACvBC,YAAY,CAAEF,QAAQ,CAACG,QAAQ,EAChC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGA,CAACC,KAAa,CAAEC,SAA6B,GAAK,CAC9E,KAAM,CAAAC,iBAAiB,CAAG,CAAC,IAAIpC,QAAQ,CAACwB,UAAU,EAAI,EAAE,CAAC,CAAC,CAC1DY,iBAAiB,CAACF,KAAK,CAAC,CAAGC,SAAsB,CAEjD,KAAM,CAAAtB,eAAe,CAAAC,aAAA,CAAAA,aAAA,IAChBd,QAAQ,MACXwB,UAAU,CAAEY,iBAAiB,EAC9B,CAED;AACA,KAAM,CAAAC,aAAa,CAAG7C,sBAAsB,CAACqB,eAAe,CAAC,CAC7D,GAAIwB,aAAa,CAACC,YAAY,EAAID,aAAa,CAACE,UAAU,CAAE,CAC1D1B,eAAe,CAACyB,YAAY,CAAGD,aAAa,CAACC,YAAY,CACzDzB,eAAe,CAAC0B,UAAU,CAAGF,aAAa,CAACE,UAAU,CACvD,CAEA;AACA,KAAM,CAAAxB,WAAW,CAAGxB,uBAAuB,CAACsB,eAAe,CAAC,CAC5DA,eAAe,CAACG,WAAW,CAAGD,WAAW,CAACC,WAAW,CAErDf,QAAQ,CAACY,eAAe,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA2B,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,YAAuB,CAAG,CAC9BC,aAAa,CAAE,CAAC,CAChBC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EACd,CAAC,CAED7C,QAAQ,CAAAa,aAAA,CAAAA,aAAA,IACHd,QAAQ,MACXwB,UAAU,CAAE,CAAC,IAAIxB,QAAQ,CAACwB,UAAU,EAAI,EAAE,CAAC,CAAEiB,YAAY,CAAC,EAC3D,CAAC,CACJ,CAAC,CAED,KAAM,CAAAM,qBAAqB,CAAIb,KAAa,EAAK,CAC/C,KAAM,CAAAE,iBAAiB,CAAG,CAAC,IAAIpC,QAAQ,CAACwB,UAAU,EAAI,EAAE,CAAC,CAAC,CAC1DY,iBAAiB,CAACY,MAAM,CAACd,KAAK,CAAE,CAAC,CAAC,CAElCjC,QAAQ,CAAAa,aAAA,CAAAA,aAAA,IACHd,QAAQ,MACXwB,UAAU,CAAEY,iBAAiB,EAC9B,CAAC,CACJ,CAAC,CAED,mBACExC,KAAA,CAAC9B,GAAG,EAACmF,SAAS,CAAC,MAAM,CAAC/C,QAAQ,CAAEe,gBAAiB,CAAAiC,QAAA,eAC/CxD,IAAA,CAACN,UAAU,EACT+D,KAAK,CAAEhD,MAAM,CAAG,oBAAoB,CAAG,kBAAmB,CAC1DiD,QAAQ,CAAC,sEAAkC,CAC5C,CAAC,cAGFxD,KAAA,CAACxB,OAAO,EACNiF,UAAU,CAAErD,QAAQ,CAACsB,UAAU,CAAI,CAAAvB,qBAAA,CAAAC,QAAQ,CAACwB,UAAU,UAAAzB,qBAAA,WAAnBA,qBAAA,CAAqB0B,MAAM,CAAG,CAAC,CAAG,CAAC,CAAI,CAAE,CAC5E6B,gBAAgB,MAChBC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eAEdxD,IAAA,CAACrB,IAAI,EAAA6E,QAAA,cACHxD,IAAA,CAACpB,SAAS,EAAA4E,QAAA,CAAC,4BAAe,CAAW,CAAC,CAClC,CAAC,cACPxD,IAAA,CAACrB,IAAI,EAAA6E,QAAA,cACHxD,IAAA,CAACpB,SAAS,EAAA4E,QAAA,CAAC,sCAAkB,CAAW,CAAC,CACrC,CAAC,cACPxD,IAAA,CAACrB,IAAI,EAAA6E,QAAA,cACHxD,IAAA,CAACpB,SAAS,EAAA4E,QAAA,CAAC,iCAAkB,CAAW,CAAC,CACrC,CAAC,EACA,CAAC,cAGVtD,KAAA,CAAC1B,KAAK,EACJuF,SAAS,CAAE,CAAE,CACbF,EAAE,CAAE,CACFG,CAAC,CAAE,CAAC,CACJF,EAAE,CAAE,CAAC,CACLG,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAEtD,KAAK,CAACuD,OAAO,CAACD,UAAU,CAACE,KAAK,CAC1CC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,CACXC,OAAO,CAAE,IAAI,CACbF,QAAQ,CAAE,UAAU,CACpBG,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbT,UAAU,CAAEtD,KAAK,CAACuD,OAAO,CAACS,OAAO,CAACC,IACpC,CACF,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACzB,UAAU,EAACwG,OAAO,CAAC,IAAI,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEkB,KAAK,CAAEnE,KAAK,CAACuD,OAAO,CAACS,OAAO,CAACC,IAAI,CAAEG,UAAU,CAAE,MAAO,CAAE,CAAAzB,QAAA,CAAC,yEAE/F,CAAY,CAAC,cAEbtD,KAAA,CAAC9B,GAAG,EAACyF,EAAE,CAAE,CAAEqB,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA5B,QAAA,eAErDxD,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEc,KAAK,CAAE,CAAEU,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAE,CAAA9B,QAAA,cAC5CxD,IAAA,CAACnB,IAAI,EAACkG,OAAO,CAAC,UAAU,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEc,MAAM,CAAE,MAAO,CAAE,CAAApB,QAAA,cACrDtD,KAAA,CAACpB,WAAW,EAAA0E,QAAA,eACVtD,KAAA,CAAC3B,UAAU,EAACwG,OAAO,CAAC,WAAW,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEmB,UAAU,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAS,CAAE,CAAA/B,QAAA,eACvGxD,IAAA,CAACb,UAAU,EAAC0E,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,gCAE/B,EAAY,CAAC,CAEZlF,QAAQ,CAACsB,UAAU,cAClB1B,KAAA,CAAC9B,GAAG,EAACyF,EAAE,CAAE,CAAEG,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,iBAAiB,CAAEC,YAAY,CAAE,KAAK,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eACvExD,IAAA,CAACzB,UAAU,EAACwG,OAAO,CAAC,OAAO,CAAClB,EAAE,CAAE,CAAEoB,UAAU,CAAE,MAAO,CAAE,CAAAzB,QAAA,CACpDlD,QAAQ,CAAC+B,YAAY,CACZ,CAAC,cACbrC,IAAA,CAAC1B,MAAM,EACLyG,OAAO,CAAC,UAAU,CAClBU,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE1D,wBAAyB,CAClC6B,EAAE,CAAE,CAAE8B,EAAE,CAAE,CAAE,CAAE,CAAAnC,QAAA,CACf,oBAED,CAAQ,CAAC,EACN,CAAC,cAENxD,IAAA,CAAC1B,MAAM,EACLyG,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfY,SAAS,MACTF,OAAO,CAAE1D,wBAAyB,CAClC6B,EAAE,CAAE,CAAEe,MAAM,CAAE,EAAG,CAAE,CACnBiB,SAAS,cAAE7F,IAAA,CAACb,UAAU,GAAE,CAAE,CAAAqE,QAAA,CAC3B,4BAED,CAAQ,CACT,EACU,CAAC,CACV,CAAC,CACJ,CAAC,cAKNxD,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEc,KAAK,CAAE,CAAEU,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAE,CAAA9B,QAAA,cAC5CxD,IAAA,CAACnB,IAAI,EAACkG,OAAO,CAAC,UAAU,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,cACrCtD,KAAA,CAACpB,WAAW,EAAA0E,QAAA,eACVtD,KAAA,CAAC3B,UAAU,EAACwG,OAAO,CAAC,WAAW,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEmB,UAAU,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAS,CAAE,CAAA/B,QAAA,eACvGxD,IAAA,CAACZ,aAAa,EAACyE,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,qGAEhC,cAAAxF,IAAA,CAAChB,OAAO,EAACyE,KAAK,CAAC,0OAA4G,CAAAD,QAAA,cACzHxD,IAAA,CAACjB,UAAU,EAAC0G,IAAI,CAAC,OAAO,CAAC5B,EAAE,CAAE,CAAEiC,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACrCxD,IAAA,CAACT,eAAe,EAACwG,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC1B,CAAC,CACN,CAAC,EACA,CAAC,cACb7F,KAAA,CAAC9B,GAAG,EAACyF,EAAE,CAAE,CAAEqB,OAAO,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA5B,QAAA,eACrDxD,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEc,KAAK,CAAE,CAAEU,EAAE,CAAE,MAAM,CAAEW,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAxC,QAAA,cAC5CxD,IAAA,CAACL,eAAe,EACdsG,KAAK,CAAC,yDAAwB,CAC9BhF,KAAK,CAAEX,QAAQ,CAACsC,YAAY,EAAI,EAAG,CACnCrC,QAAQ,CAAEA,CAAA,GAAM,CAAC,CAAG;AAAA,CACpB2F,QAAQ,MACRC,QAAQ,MACT,CAAC,CACC,CAAC,cACNnG,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEc,KAAK,CAAE,CAAEU,EAAE,CAAE,MAAM,CAAEW,EAAE,CAAE,KAAM,CAAE,CAAE,CAAAxC,QAAA,cAC5CxD,IAAA,CAACL,eAAe,EACdsG,KAAK,CAAC,mDAAyB,CAC/BhF,KAAK,CAAEX,QAAQ,CAACuC,UAAU,EAAI,EAAG,CACjCtC,QAAQ,CAAEA,CAAA,GAAM,CAAC,CAAG;AAAA,CACpB2F,QAAQ,MACRC,QAAQ,MACT,CAAC,CACC,CAAC,EACH,CAAC,EACK,CAAC,CACV,CAAC,CACJ,CAAC,cAGNnG,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEc,KAAK,CAAE,CAAEU,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,KAAM,CAAE,CAAE,CAAA9B,QAAA,cAC5CxD,IAAA,CAACnB,IAAI,EAACkG,OAAO,CAAC,UAAU,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,cACrCtD,KAAA,CAACpB,WAAW,EAAA0E,QAAA,eACVtD,KAAA,CAAC3B,UAAU,EAACwG,OAAO,CAAC,WAAW,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEmB,UAAU,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAS,CAAE,CAAA/B,QAAA,eACvGxD,IAAA,CAACV,kBAAkB,EAACuE,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,mFAErC,cAAAxF,IAAA,CAAChB,OAAO,EAACyE,KAAK,CAAC,uMAAsF,CAAAD,QAAA,cACnGxD,IAAA,CAACjB,UAAU,EAAC0G,IAAI,CAAC,OAAO,CAAC5B,EAAE,CAAE,CAAEiC,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACrCxD,IAAA,CAACT,eAAe,EAACwG,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC1B,CAAC,CACN,CAAC,EACA,CAAC,cACb/F,IAAA,CAAC3B,SAAS,EACRuH,SAAS,MACTK,KAAK,CAAC,8DAA6B,CACnCjF,IAAI,CAAC,aAAa,CAClBoF,IAAI,CAAC,MAAM,CACXnF,KAAK,CAAEX,QAAQ,CAACgB,WAAW,CAAGhB,QAAQ,CAACgB,WAAW,CAAC+E,cAAc,CAAC,OAAO,CAAC,CAAG,MAAM,CAAG,OAAQ,CAC9FC,SAAS,CAAE,CACTC,KAAK,CAAE,CACLC,QAAQ,CAAE,IACZ,CACF,CAAE,CACF3C,EAAE,CAAE,CACF,SAAS,CAAE,CACToB,UAAU,CAAE,MAAM,CAClBD,KAAK,CAAEnE,KAAK,CAACuD,OAAO,CAACqC,OAAO,CAAC3B,IAAI,CACjC4B,eAAe,CAAE7F,KAAK,CAACuD,OAAO,CAACuC,MAAM,CAACC,KACxC,CACF,CAAE,CACH,CAAC,EACS,CAAC,CACV,CAAC,CACJ,CAAC,cAGN5G,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEc,KAAK,CAAE,MAAO,CAAE,CAAAnB,QAAA,cACzBxD,IAAA,CAACnB,IAAI,EAACkG,OAAO,CAAC,UAAU,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,cACrCtD,KAAA,CAACpB,WAAW,EAAA0E,QAAA,eACVtD,KAAA,CAAC3B,UAAU,EAACwG,OAAO,CAAC,WAAW,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEmB,UAAU,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEK,UAAU,CAAE,QAAS,CAAE,CAAA/B,QAAA,eACvGxD,IAAA,CAACX,eAAe,EAACwE,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,wCAEpC,EAAY,CAAC,cACbxF,IAAA,CAAC3B,SAAS,EACRuH,SAAS,MACTK,KAAK,CAAC,6DAA4B,CAClCjF,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAEX,QAAQ,CAACuG,WAAW,EAAI,EAAG,CAClCtG,QAAQ,CAAEO,iBAAkB,CAC5BgG,SAAS,CAAG/F,CAAC,EAAK,CAChB;AACA,GAAIA,CAAC,CAACgG,GAAG,GAAK,OAAO,EAAI,CAAChG,CAAC,CAACiG,QAAQ,CAAE,CACpCjG,CAAC,CAACkG,eAAe,CAAC,CAAC,CACrB,CACF,CAAE,CACFC,SAAS,MACTC,IAAI,CAAE,CAAE,CACRC,WAAW,CAAC,0GAAyD,CACtE,CAAC,EACS,CAAC,CACV,CAAC,CACJ,CAAC,EACH,CAAC,cAENpH,IAAA,CAACJ,cAAc,EACbyH,IAAI,CAAE1G,kBAAmB,CACzB2G,OAAO,CAAErF,yBAA0B,CACnCsF,gBAAgB,CAAErF,oBAAqB,CACxC,CAAC,EACG,CAAC,cAGRlC,IAAA,CAACzB,UAAU,EAACwG,OAAO,CAAC,IAAI,CAAClB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE6B,EAAE,CAAE,CAAC,CAAEV,UAAU,CAAE,MAAM,CAAED,KAAK,CAAEnE,KAAK,CAACuD,OAAO,CAACS,OAAO,CAACC,IAAK,CAAE,CAAAtB,QAAA,CAAC,iCAEtG,CAAY,CAAC,CAEZ,CAAClD,QAAQ,CAACwB,UAAU,EAAI,EAAE,EAAE0F,GAAG,CAAC,CAAC/E,SAAS,CAAED,KAAK,gBAChDxC,IAAA,CAACR,aAAa,EAEZiD,SAAS,CAAEA,SAAU,CACrBlC,QAAQ,CAAGkH,gBAAgB,EAAKlF,qBAAqB,CAACC,KAAK,CAAEiF,gBAAgB,CAAE,CAC/EC,QAAQ,CAAEA,CAAA,GAAMrE,qBAAqB,CAACb,KAAK,CAAE,CAC7CmF,UAAU,CAAE,IAAK,EAJZnF,KAKN,CACF,CAAC,cAEFxC,IAAA,CAAC1B,MAAM,EACLyG,OAAO,CAAC,UAAU,CAClBc,SAAS,cAAE7F,IAAA,CAACd,OAAO,GAAE,CAAE,CACvBwG,OAAO,CAAE5C,kBAAmB,CAC5Be,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CACf,2BAED,CAAQ,CAAC,cAGTxD,IAAA,CAACP,yBAAyB,EAACa,QAAQ,CAAEA,QAAS,CAAE,CAAC,cAEjDN,IAAA,CAACvB,OAAO,EAACoF,EAAE,CAAE,CAAE+D,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1B5H,IAAA,CAAC5B,GAAG,EAACyF,EAAE,CAAE,CAAEqB,OAAO,CAAE,MAAM,CAAE2C,cAAc,CAAE,UAAU,CAAElC,EAAE,CAAE,CAAE,CAAE,CAAAnC,QAAA,cAC9DxD,IAAA,CAAC1B,MAAM,EACL8H,IAAI,CAAC,QAAQ,CACbrB,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfS,IAAI,CAAC,OAAO,CACZU,QAAQ,CAAEzF,OAAO,EAAI,CAACJ,QAAQ,CAACsB,UAAW,CAAA4B,QAAA,CAEzC9C,OAAO,CAAG,eAAe,CAAID,MAAM,CAAG,mBAAmB,CAAG,cAAe,CACtE,CAAC,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAN,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}