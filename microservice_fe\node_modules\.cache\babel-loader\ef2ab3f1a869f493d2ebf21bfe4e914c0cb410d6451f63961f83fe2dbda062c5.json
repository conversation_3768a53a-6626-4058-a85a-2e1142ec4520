{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{Box,Button,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Chip,IconButton,Typography,TextField,Card,CardContent,CardActions,Divider,useMediaQuery,useTheme}from'@mui/material';import AddIcon from'@mui/icons-material/Add';import SearchIcon from'@mui/icons-material/Search';import VisibilityIcon from'@mui/icons-material/Visibility';import EditIcon from'@mui/icons-material/Edit';import{ContractStatusMap}from'../models';import{contractService}from'../services/contract/contractService';import{Loading<PERSON>pinner,ErrorAlert,PageHeader}from'../components/common';import{formatDateLocalized}from'../utils/dateUtils';import{formatCurrency}from'../utils/currencyUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ContractsListPage=()=>{const navigate=useNavigate();const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('md'));const[contracts,setContracts]=useState([]);const[filteredContracts,setFilteredContracts]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[searchTerm,setSearchTerm]=useState('');const fetchContracts=async()=>{setLoading(true);try{const data=await contractService.getAllContracts();// Sort contracts by creation date (newest first)\nconst sortedData=[...data].sort((a,b)=>{const dateA=new Date(a.createdAt||'');const dateB=new Date(b.createdAt||'');return dateB.getTime()-dateA.getTime();});setContracts(sortedData);setFilteredContracts(sortedData);}catch(err){setError(err.message||'Đã xảy ra lỗi khi tải danh sách hợp đồng');}finally{setLoading(false);}};useEffect(()=>{fetchContracts();},[]);// Listen for refresh flag from localStorage\nuseEffect(()=>{const handleStorageChange=()=>{const needsRefresh=localStorage.getItem('contractsListNeedsRefresh');if(needsRefresh==='true'){localStorage.removeItem('contractsListNeedsRefresh');fetchContracts();}};// Check on component mount\nhandleStorageChange();// Listen for storage changes\nwindow.addEventListener('storage',handleStorageChange);// Also listen for focus events (when user returns to tab)\nwindow.addEventListener('focus',handleStorageChange);return()=>{window.removeEventListener('storage',handleStorageChange);window.removeEventListener('focus',handleStorageChange);};},[]);useEffect(()=>{if(searchTerm.trim()===''){setFilteredContracts(contracts);return;}const lowercasedSearch=searchTerm.toLowerCase();const filtered=contracts.filter(contract=>{var _contract$customerNam;return String(contract.id).includes(lowercasedSearch)||((_contract$customerNam=contract.customerName)===null||_contract$customerNam===void 0?void 0:_contract$customerNam.toLowerCase().includes(lowercasedSearch));});setFilteredContracts(filtered);},[searchTerm,contracts]);const handleCreateContract=()=>{navigate('/contracts/create');};const handleViewContract=id=>{navigate(\"/contracts/\".concat(id));};const handleEditContract=id=>{navigate(\"/contracts/edit/\".concat(id));};const getStatusColor=status=>{switch(status){case 0:// Pending\nreturn'warning';case 1:// Active\nreturn'success';case 2:// Completed\nreturn'info';case 3:// Cancelled\nreturn'error';default:return'default';}};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{});}if(error){return/*#__PURE__*/_jsx(ErrorAlert,{message:error});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(PageHeader,{title:\"H\\u1EE3p \\u0111\\u1ED3ng\",subtitle:\"Qu\\u1EA3n l\\xFD h\\u1EE3p \\u0111\\u1ED3ng kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:handleCreateContract,children:\"T\\u1EA1o h\\u1EE3p \\u0111\\u1ED3ng\"})]}),/*#__PURE__*/_jsx(Paper,{sx:{p:2,mb:3},children:/*#__PURE__*/_jsxs(Box,{sx:{position:'relative'},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"T\\xECm ki\\u1EBFm h\\u1EE3p \\u0111\\u1ED3ng theo ID, t\\xEAn kh\\xE1ch h\\xE0ng ho\\u1EB7c \\u0111\\u1ECBa ch\\u1EC9\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),sx:{pl:4}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',left:10,top:'50%',transform:'translateY(-50%)'},children:/*#__PURE__*/_jsx(SearchIcon,{color:\"action\"})})]})}),filteredContracts.length===0?/*#__PURE__*/_jsxs(Paper,{sx:{p:4,textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:\"Kh\\xF4ng t\\xECm th\\u1EA5y h\\u1EE3p \\u0111\\u1ED3ng n\\xE0o\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:handleCreateContract,sx:{mt:2},children:\"T\\u1EA1o h\\u1EE3p \\u0111\\u1ED3ng \\u0111\\u1EA7u ti\\xEAn c\\u1EE7a b\\u1EA1n\"})]}):isMobile?/*#__PURE__*/// Mobile view - card list\n_jsx(Box,{sx:{display:'flex',flexDirection:'column',gap:2},children:filteredContracts.map(contract=>/*#__PURE__*/_jsx(Box,{sx:{width:'100%'},children:/*#__PURE__*/_jsxs(Card,{variant:\"outlined\",children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"#\",contract.id]}),/*#__PURE__*/_jsx(Chip,{label:ContractStatusMap[contract.status||0],color:getStatusColor(contract.status||0),size:\"small\"})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",color:\"text.secondary\",gutterBottom:true,children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Kh\\xE1ch h\\xE0ng:\"}),\" \",contract.customerName||\"Kh\\xE1ch h\\xE0ng #\".concat(contract.customerId)]}),/*#__PURE__*/_jsx(Divider,{sx:{my:1}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',mx:-1},children:[/*#__PURE__*/_jsxs(Box,{sx:{width:'50%',p:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:formatDateLocalized(contract.startingDate)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:'50%',p:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y k\\u1EBFt th\\xFAc\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:formatDateLocalized(contract.endingDate)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:'50%',p:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"T\\u1ED5ng ti\\u1EC1n\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"bold\",children:formatCurrency(contract.totalAmount)})]}),/*#__PURE__*/_jsxs(Box,{sx:{width:'50%',p:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Ng\\xE0y t\\u1EA1o\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:formatDateLocalized(contract.createdAt||'')})]})]})]}),/*#__PURE__*/_jsxs(CardActions,{children:[/*#__PURE__*/_jsx(Button,{size:\"small\",startIcon:/*#__PURE__*/_jsx(VisibilityIcon,{}),onClick:()=>handleViewContract(contract.id),children:\"Xem\"}),/*#__PURE__*/_jsx(Button,{size:\"small\",startIcon:/*#__PURE__*/_jsx(EditIcon,{}),onClick:()=>handleEditContract(contract.id),children:\"S\\u1EEDa\"})]})]})},contract.id))}):/*#__PURE__*/// Desktop view - table\n_jsx(TableContainer,{component:Paper,children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"M\\xE3 h\\u1EE3p \\u0111\\u1ED3ng\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Kh\\xE1ch h\\xE0ng\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ng\\xE0y b\\u1EAFt \\u0111\\u1EA7u\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ng\\xE0y k\\u1EBFt th\\xFAc\"}),/*#__PURE__*/_jsx(TableCell,{children:\"T\\u1ED5ng ti\\u1EC1n\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Tr\\u1EA1ng th\\xE1i\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ng\\xE0y t\\u1EA1o\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Thao t\\xE1c\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:filteredContracts.map(contract=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsxs(TableCell,{children:[\"#\",contract.id]}),/*#__PURE__*/_jsx(TableCell,{children:contract.customerName||\"Kh\\xE1ch h\\xE0ng #\".concat(contract.customerId)}),/*#__PURE__*/_jsx(TableCell,{children:formatDateLocalized(contract.startingDate)}),/*#__PURE__*/_jsx(TableCell,{children:formatDateLocalized(contract.endingDate)}),/*#__PURE__*/_jsx(TableCell,{children:formatCurrency(contract.totalAmount)}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:ContractStatusMap[contract.status||0],color:getStatusColor(contract.status||0),size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:formatDateLocalized(contract.createdAt||'')}),/*#__PURE__*/_jsxs(TableCell,{children:[/*#__PURE__*/_jsx(IconButton,{color:\"primary\",onClick:()=>handleViewContract(contract.id),title:\"Xem\",children:/*#__PURE__*/_jsx(VisibilityIcon,{})}),/*#__PURE__*/_jsx(IconButton,{color:\"secondary\",onClick:()=>handleEditContract(contract.id),title:\"S\\u1EEDa\",children:/*#__PURE__*/_jsx(EditIcon,{})})]})]},contract.id))})]})})]});};export default ContractsListPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "IconButton", "Typography", "TextField", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Divider", "useMediaQuery", "useTheme", "AddIcon", "SearchIcon", "VisibilityIcon", "EditIcon", "ContractStatusMap", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "formatDateLocalized", "formatCurrency", "jsx", "_jsx", "jsxs", "_jsxs", "ContractsListPage", "navigate", "theme", "isMobile", "breakpoints", "down", "contracts", "setContracts", "filteredContracts", "setFilteredContracts", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "fetchContracts", "data", "getAllContracts", "sortedData", "sort", "a", "b", "dateA", "Date", "createdAt", "dateB", "getTime", "err", "message", "handleStorageChange", "needsRefresh", "localStorage", "getItem", "removeItem", "window", "addEventListener", "removeEventListener", "trim", "lowercasedSearch", "toLowerCase", "filtered", "filter", "contract", "_contract$customerNam", "String", "id", "includes", "customerName", "handleCreateContract", "handleViewContract", "concat", "handleEditContract", "getStatusColor", "status", "children", "sx", "display", "justifyContent", "alignItems", "mb", "title", "subtitle", "variant", "color", "startIcon", "onClick", "p", "position", "fullWidth", "placeholder", "value", "onChange", "e", "target", "pl", "left", "top", "transform", "length", "textAlign", "mt", "flexDirection", "gap", "map", "width", "label", "size", "gutterBottom", "customerId", "my", "flexWrap", "mx", "startingDate", "endingDate", "fontWeight", "totalAmount", "component"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/ContractsListPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  IconButton,\n  Typography,\n  TextField,\n  Card,\n  CardContent,\n  CardActions,\n  Divider,\n  useMediaQuery,\n  useTheme,\n} from '@mui/material';\nimport AddIcon from '@mui/icons-material/Add';\nimport SearchIcon from '@mui/icons-material/Search';\nimport VisibilityIcon from '@mui/icons-material/Visibility';\nimport EditIcon from '@mui/icons-material/Edit';\nimport { CustomerContract, ContractStatusMap } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, PageHeader } from '../components/common';\nimport { formatDateLocalized } from '../utils/dateUtils';\nimport { formatCurrency } from '../utils/currencyUtils';\n\nconst ContractsListPage: React.FC = () => {\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const [contracts, setContracts] = useState<CustomerContract[]>([]);\n  const [filteredContracts, setFilteredContracts] = useState<CustomerContract[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const fetchContracts = async () => {\n    setLoading(true);\n    try {\n      const data = await contractService.getAllContracts();\n      // Sort contracts by creation date (newest first)\n      const sortedData = [...data].sort((a, b) => {\n        const dateA = new Date(a.createdAt || '');\n        const dateB = new Date(b.createdAt || '');\n        return dateB.getTime() - dateA.getTime();\n      });\n      setContracts(sortedData);\n      setFilteredContracts(sortedData);\n    } catch (err: any) {\n      setError(err.message || 'Đã xảy ra lỗi khi tải danh sách hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchContracts();\n  }, []);\n\n  // Listen for refresh flag from localStorage\n  useEffect(() => {\n    const handleStorageChange = () => {\n      const needsRefresh = localStorage.getItem('contractsListNeedsRefresh');\n      if (needsRefresh === 'true') {\n        localStorage.removeItem('contractsListNeedsRefresh');\n        fetchContracts();\n      }\n    };\n\n    // Check on component mount\n    handleStorageChange();\n\n    // Listen for storage changes\n    window.addEventListener('storage', handleStorageChange);\n\n    // Also listen for focus events (when user returns to tab)\n    window.addEventListener('focus', handleStorageChange);\n\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('focus', handleStorageChange);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (searchTerm.trim() === '') {\n      setFilteredContracts(contracts);\n      return;\n    }\n\n    const lowercasedSearch = searchTerm.toLowerCase();\n    const filtered = contracts.filter(\n      (contract) =>\n        String(contract.id).includes(lowercasedSearch) ||\n        contract.customerName?.toLowerCase().includes(lowercasedSearch)\n    );\n    setFilteredContracts(filtered);\n  }, [searchTerm, contracts]);\n\n  const handleCreateContract = () => {\n    navigate('/contracts/create');\n  };\n\n  const handleViewContract = (id: number) => {\n    navigate(`/contracts/${id}`);\n  };\n\n  const handleEditContract = (id: number) => {\n    navigate(`/contracts/edit/${id}`);\n  };\n\n  const getStatusColor = (status: number) => {\n    switch (status) {\n      case 0: // Pending\n        return 'warning';\n      case 1: // Active\n        return 'success';\n      case 2: // Completed\n        return 'info';\n      case 3: // Cancelled\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  if (error) {\n    return <ErrorAlert message={error} />;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <PageHeader title=\"Hợp đồng\" subtitle=\"Quản lý hợp đồng khách hàng\" />\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateContract}\n        >\n          Tạo hợp đồng\n        </Button>\n      </Box>\n\n      <Paper sx={{ p: 2, mb: 3 }}>\n        <Box sx={{ position: 'relative' }}>\n          <TextField\n            fullWidth\n            placeholder=\"Tìm kiếm hợp đồng theo ID, tên khách hàng hoặc địa chỉ\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            sx={{ pl: 4 }}\n          />\n          <Box sx={{ position: 'absolute', left: 10, top: '50%', transform: 'translateY(-50%)' }}>\n            <SearchIcon color=\"action\" />\n          </Box>\n        </Box>\n      </Paper>\n\n      {filteredContracts.length === 0 ? (\n        <Paper sx={{ p: 4, textAlign: 'center' }}>\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            Không tìm thấy hợp đồng nào\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<AddIcon />}\n            onClick={handleCreateContract}\n            sx={{ mt: 2 }}\n          >\n            Tạo hợp đồng đầu tiên của bạn\n          </Button>\n        </Paper>\n      ) : isMobile ? (\n        // Mobile view - card list\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n          {filteredContracts.map((contract) => (\n            <Box sx={{ width: '100%' }} key={contract.id}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"h6\">#{contract.id}</Typography>\n                    <Chip\n                      label={ContractStatusMap[contract.status || 0]}\n                      color={getStatusColor(contract.status || 0)}\n                      size=\"small\"\n                    />\n                  </Box>\n\n                  <Typography variant=\"subtitle1\" color=\"text.secondary\" gutterBottom>\n                    <strong>Khách hàng:</strong> {contract.customerName || `Khách hàng #${contract.customerId}`}\n                  </Typography>\n\n                  <Divider sx={{ my: 1 }} />\n\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', mx: -1 }}>\n                    <Box sx={{ width: '50%', p: 1 }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">Ngày bắt đầu</Typography>\n                      <Typography variant=\"body2\">{formatDateLocalized(contract.startingDate)}</Typography>\n                    </Box>\n                    <Box sx={{ width: '50%', p: 1 }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">Ngày kết thúc</Typography>\n                      <Typography variant=\"body2\">{formatDateLocalized(contract.endingDate)}</Typography>\n                    </Box>\n                    <Box sx={{ width: '50%', p: 1 }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">Tổng tiền</Typography>\n                      <Typography variant=\"body2\" fontWeight=\"bold\">{formatCurrency(contract.totalAmount)}</Typography>\n                    </Box>\n                    <Box sx={{ width: '50%', p: 1 }}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">Ngày tạo</Typography>\n                      <Typography variant=\"body2\">{formatDateLocalized(contract.createdAt || '')}</Typography>\n                    </Box>\n                  </Box>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    size=\"small\"\n                    startIcon={<VisibilityIcon />}\n                    onClick={() => handleViewContract(contract.id!)}\n                  >\n                    Xem\n                  </Button>\n                  <Button\n                    size=\"small\"\n                    startIcon={<EditIcon />}\n                    onClick={() => handleEditContract(contract.id!)}\n                  >\n                    Sửa\n                  </Button>\n                </CardActions>\n              </Card>\n            </Box>\n          ))}\n        </Box>\n      ) : (\n        // Desktop view - table\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Mã hợp đồng</TableCell>\n                <TableCell>Khách hàng</TableCell>\n                <TableCell>Ngày bắt đầu</TableCell>\n                <TableCell>Ngày kết thúc</TableCell>\n                <TableCell>Tổng tiền</TableCell>\n                <TableCell>Trạng thái</TableCell>\n                <TableCell>Ngày tạo</TableCell>\n                <TableCell>Thao tác</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {filteredContracts.map((contract) => (\n                <TableRow key={contract.id}>\n                  <TableCell>#{contract.id}</TableCell>\n                  <TableCell>{contract.customerName || `Khách hàng #${contract.customerId}`}</TableCell>\n                  <TableCell>{formatDateLocalized(contract.startingDate)}</TableCell>\n                  <TableCell>{formatDateLocalized(contract.endingDate)}</TableCell>\n                  <TableCell>{formatCurrency(contract.totalAmount)}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={ContractStatusMap[contract.status || 0]}\n                      color={getStatusColor(contract.status || 0)}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>{formatDateLocalized(contract.createdAt || '')}</TableCell>\n                  <TableCell>\n                    <IconButton\n                      color=\"primary\"\n                      onClick={() => handleViewContract(contract.id!)}\n                      title=\"Xem\"\n                    >\n                      <VisibilityIcon />\n                    </IconButton>\n                    <IconButton\n                      color=\"secondary\"\n                      onClick={() => handleEditContract(contract.id!)}\n                      title=\"Sửa\"\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n};\n\nexport default ContractsListPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,GAAG,CACHC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,IAAI,CACJC,UAAU,CACVC,UAAU,CACVC,SAAS,CACTC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,OAAO,CACPC,aAAa,CACbC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,OAA2BC,iBAAiB,KAAQ,WAAW,CAC/D,OAASC,eAAe,KAAQ,sCAAsC,CACtE,OAASC,cAAc,CAAEC,UAAU,CAAEC,UAAU,KAAQ,sBAAsB,CAC7E,OAASC,mBAAmB,KAAQ,oBAAoB,CACxD,OAASC,cAAc,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAAC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqC,KAAK,CAAGlB,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAmB,QAAQ,CAAGpB,aAAa,CAACmB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG5C,QAAQ,CAAqB,EAAE,CAAC,CAClE,KAAM,CAAC6C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9C,QAAQ,CAAqB,EAAE,CAAC,CAClF,KAAM,CAAC+C,OAAO,CAAEC,UAAU,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACiD,KAAK,CAAEC,QAAQ,CAAC,CAAGlD,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAAqD,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjCL,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAM,IAAI,CAAG,KAAM,CAAA3B,eAAe,CAAC4B,eAAe,CAAC,CAAC,CACpD;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,GAAGF,IAAI,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1C,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,IAAI,CAACH,CAAC,CAACI,SAAS,EAAI,EAAE,CAAC,CACzC,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAF,IAAI,CAACF,CAAC,CAACG,SAAS,EAAI,EAAE,CAAC,CACzC,MAAO,CAAAC,KAAK,CAACC,OAAO,CAAC,CAAC,CAAGJ,KAAK,CAACI,OAAO,CAAC,CAAC,CAC1C,CAAC,CAAC,CACFpB,YAAY,CAACY,UAAU,CAAC,CACxBV,oBAAoB,CAACU,UAAU,CAAC,CAClC,CAAE,MAAOS,GAAQ,CAAE,CACjBf,QAAQ,CAACe,GAAG,CAACC,OAAO,EAAI,0CAA0C,CAAC,CACrE,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED/C,SAAS,CAAC,IAAM,CACdoD,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACApD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,YAAY,CAAGC,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CACtE,GAAIF,YAAY,GAAK,MAAM,CAAE,CAC3BC,YAAY,CAACE,UAAU,CAAC,2BAA2B,CAAC,CACpDlB,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAED;AACAc,mBAAmB,CAAC,CAAC,CAErB;AACAK,MAAM,CAACC,gBAAgB,CAAC,SAAS,CAAEN,mBAAmB,CAAC,CAEvD;AACAK,MAAM,CAACC,gBAAgB,CAAC,OAAO,CAAEN,mBAAmB,CAAC,CAErD,MAAO,IAAM,CACXK,MAAM,CAACE,mBAAmB,CAAC,SAAS,CAAEP,mBAAmB,CAAC,CAC1DK,MAAM,CAACE,mBAAmB,CAAC,OAAO,CAAEP,mBAAmB,CAAC,CAC1D,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAENlE,SAAS,CAAC,IAAM,CACd,GAAIkD,UAAU,CAACwB,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC5B7B,oBAAoB,CAACH,SAAS,CAAC,CAC/B,OACF,CAEA,KAAM,CAAAiC,gBAAgB,CAAGzB,UAAU,CAAC0B,WAAW,CAAC,CAAC,CACjD,KAAM,CAAAC,QAAQ,CAAGnC,SAAS,CAACoC,MAAM,CAC9BC,QAAQ,OAAAC,qBAAA,OACP,CAAAC,MAAM,CAACF,QAAQ,CAACG,EAAE,CAAC,CAACC,QAAQ,CAACR,gBAAgB,CAAC,IAAAK,qBAAA,CAC9CD,QAAQ,CAACK,YAAY,UAAAJ,qBAAA,iBAArBA,qBAAA,CAAuBJ,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,gBAAgB,CAAC,GACnE,CAAC,CACD9B,oBAAoB,CAACgC,QAAQ,CAAC,CAChC,CAAC,CAAE,CAAC3B,UAAU,CAAER,SAAS,CAAC,CAAC,CAE3B,KAAM,CAAA2C,oBAAoB,CAAGA,CAAA,GAAM,CACjChD,QAAQ,CAAC,mBAAmB,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAiD,kBAAkB,CAAIJ,EAAU,EAAK,CACzC7C,QAAQ,eAAAkD,MAAA,CAAeL,EAAE,CAAE,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAM,kBAAkB,CAAIN,EAAU,EAAK,CACzC7C,QAAQ,oBAAAkD,MAAA,CAAoBL,EAAE,CAAE,CAAC,CACnC,CAAC,CAED,KAAM,CAAAO,cAAc,CAAIC,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,EAAC,CAAE;AACN,MAAO,SAAS,CAClB,IAAK,EAAC,CAAE;AACN,MAAO,SAAS,CAClB,IAAK,EAAC,CAAE;AACN,MAAO,MAAM,CACf,IAAK,EAAC,CAAE;AACN,MAAO,OAAO,CAChB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,GAAI5C,OAAO,CAAE,CACX,mBAAOb,IAAA,CAACN,cAAc,GAAE,CAAC,CAC3B,CAEA,GAAIqB,KAAK,CAAE,CACT,mBAAOf,IAAA,CAACL,UAAU,EAACqC,OAAO,CAAEjB,KAAM,CAAE,CAAC,CACvC,CAEA,mBACEb,KAAA,CAACjC,GAAG,EAAAyF,QAAA,eACFxD,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzF1D,IAAA,CAACJ,UAAU,EAACoE,KAAK,CAAC,yBAAU,CAACC,QAAQ,CAAC,0DAA6B,CAAE,CAAC,cACtEjE,IAAA,CAAC9B,MAAM,EACLgG,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfC,SAAS,cAAEpE,IAAA,CAACZ,OAAO,GAAE,CAAE,CACvBiF,OAAO,CAAEjB,oBAAqB,CAAAM,QAAA,CAC/B,kCAED,CAAQ,CAAC,EACN,CAAC,cAEN1D,IAAA,CAAC7B,KAAK,EAACwF,EAAE,CAAE,CAAEW,CAAC,CAAE,CAAC,CAAEP,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACzBxD,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAEY,QAAQ,CAAE,UAAW,CAAE,CAAAb,QAAA,eAChC1D,IAAA,CAACnB,SAAS,EACR2F,SAAS,MACTC,WAAW,CAAC,4GAAwD,CACpEC,KAAK,CAAEzD,UAAW,CAClB0D,QAAQ,CAAGC,CAAC,EAAK1D,aAAa,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/Cf,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACF9E,IAAA,CAAC/B,GAAG,EAAC0F,EAAE,CAAE,CAAEY,QAAQ,CAAE,UAAU,CAAEQ,IAAI,CAAE,EAAE,CAAEC,GAAG,CAAE,KAAK,CAAEC,SAAS,CAAE,kBAAmB,CAAE,CAAAvB,QAAA,cACrF1D,IAAA,CAACX,UAAU,EAAC8E,KAAK,CAAC,QAAQ,CAAE,CAAC,CAC1B,CAAC,EACH,CAAC,CACD,CAAC,CAEPxD,iBAAiB,CAACuE,MAAM,GAAK,CAAC,cAC7BhF,KAAA,CAAC/B,KAAK,EAACwF,EAAE,CAAE,CAAEW,CAAC,CAAE,CAAC,CAAEa,SAAS,CAAE,QAAS,CAAE,CAAAzB,QAAA,eACvC1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,IAAI,CAACC,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CAAC,0DAEhD,CAAY,CAAC,cACb1D,IAAA,CAAC9B,MAAM,EACLgG,OAAO,CAAC,WAAW,CACnBC,KAAK,CAAC,SAAS,CACfC,SAAS,cAAEpE,IAAA,CAACZ,OAAO,GAAE,CAAE,CACvBiF,OAAO,CAAEjB,oBAAqB,CAC9BO,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CACf,0EAED,CAAQ,CAAC,EACJ,CAAC,CACNpD,QAAQ,cACV;AACAN,IAAA,CAAC/B,GAAG,EAAC0F,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEyB,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA5B,QAAA,CAC3D/C,iBAAiB,CAAC4E,GAAG,CAAEzC,QAAQ,eAC9B9C,IAAA,CAAC/B,GAAG,EAAC0F,EAAE,CAAE,CAAE6B,KAAK,CAAE,MAAO,CAAE,CAAA9B,QAAA,cACzBxD,KAAA,CAACpB,IAAI,EAACoF,OAAO,CAAC,UAAU,CAAAR,QAAA,eACtBxD,KAAA,CAACnB,WAAW,EAAA2E,QAAA,eACVxD,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzFxD,KAAA,CAACtB,UAAU,EAACsF,OAAO,CAAC,IAAI,CAAAR,QAAA,EAAC,GAAC,CAACZ,QAAQ,CAACG,EAAE,EAAa,CAAC,cACpDjD,IAAA,CAACtB,IAAI,EACH+G,KAAK,CAAEjG,iBAAiB,CAACsD,QAAQ,CAACW,MAAM,EAAI,CAAC,CAAE,CAC/CU,KAAK,CAAEX,cAAc,CAACV,QAAQ,CAACW,MAAM,EAAI,CAAC,CAAE,CAC5CiC,IAAI,CAAC,OAAO,CACb,CAAC,EACC,CAAC,cAENxF,KAAA,CAACtB,UAAU,EAACsF,OAAO,CAAC,WAAW,CAACC,KAAK,CAAC,gBAAgB,CAACwB,YAAY,MAAAjC,QAAA,eACjE1D,IAAA,WAAA0D,QAAA,CAAQ,mBAAW,CAAQ,CAAC,IAAC,CAACZ,QAAQ,CAACK,YAAY,uBAAAG,MAAA,CAAmBR,QAAQ,CAAC8C,UAAU,CAAE,EACjF,CAAC,cAEb5F,IAAA,CAACf,OAAO,EAAC0E,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1B3F,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEkC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAE,CAAC,CAAE,CAAE,CAAArC,QAAA,eACrDxD,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAE6B,KAAK,CAAE,KAAK,CAAElB,CAAC,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAC9B1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CAAC,gCAAY,CAAY,CAAC,cAC5E1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAAAR,QAAA,CAAE7D,mBAAmB,CAACiD,QAAQ,CAACkD,YAAY,CAAC,CAAa,CAAC,EAClF,CAAC,cACN9F,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAE6B,KAAK,CAAE,KAAK,CAAElB,CAAC,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAC9B1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CAAC,0BAAa,CAAY,CAAC,cAC7E1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAAAR,QAAA,CAAE7D,mBAAmB,CAACiD,QAAQ,CAACmD,UAAU,CAAC,CAAa,CAAC,EAChF,CAAC,cACN/F,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAE6B,KAAK,CAAE,KAAK,CAAElB,CAAC,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAC9B1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CAAC,qBAAS,CAAY,CAAC,cACzE1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACgC,UAAU,CAAC,MAAM,CAAAxC,QAAA,CAAE5D,cAAc,CAACgD,QAAQ,CAACqD,WAAW,CAAC,CAAa,CAAC,EAC9F,CAAC,cACNjG,KAAA,CAACjC,GAAG,EAAC0F,EAAE,CAAE,CAAE6B,KAAK,CAAE,KAAK,CAAElB,CAAC,CAAE,CAAE,CAAE,CAAAZ,QAAA,eAC9B1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CAAC,kBAAQ,CAAY,CAAC,cACxE1D,IAAA,CAACpB,UAAU,EAACsF,OAAO,CAAC,OAAO,CAAAR,QAAA,CAAE7D,mBAAmB,CAACiD,QAAQ,CAAClB,SAAS,EAAI,EAAE,CAAC,CAAa,CAAC,EACrF,CAAC,EACH,CAAC,EACK,CAAC,cACd1B,KAAA,CAAClB,WAAW,EAAA0E,QAAA,eACV1D,IAAA,CAAC9B,MAAM,EACLwH,IAAI,CAAC,OAAO,CACZtB,SAAS,cAAEpE,IAAA,CAACV,cAAc,GAAE,CAAE,CAC9B+E,OAAO,CAAEA,CAAA,GAAMhB,kBAAkB,CAACP,QAAQ,CAACG,EAAG,CAAE,CAAAS,QAAA,CACjD,KAED,CAAQ,CAAC,cACT1D,IAAA,CAAC9B,MAAM,EACLwH,IAAI,CAAC,OAAO,CACZtB,SAAS,cAAEpE,IAAA,CAACT,QAAQ,GAAE,CAAE,CACxB8E,OAAO,CAAEA,CAAA,GAAMd,kBAAkB,CAACT,QAAQ,CAACG,EAAG,CAAE,CAAAS,QAAA,CACjD,UAED,CAAQ,CAAC,EACE,CAAC,EACV,CAAC,EArDwBZ,QAAQ,CAACG,EAsDrC,CACN,CAAC,CACC,CAAC,cAEN;AACAjD,IAAA,CAACzB,cAAc,EAAC6H,SAAS,CAAEjI,KAAM,CAAAuF,QAAA,cAC/BxD,KAAA,CAAC9B,KAAK,EAAAsF,QAAA,eACJ1D,IAAA,CAACxB,SAAS,EAAAkF,QAAA,cACRxD,KAAA,CAACzB,QAAQ,EAAAiF,QAAA,eACP1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,+BAAW,CAAW,CAAC,cAClC1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,kBAAU,CAAW,CAAC,cACjC1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,gCAAY,CAAW,CAAC,cACnC1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,0BAAa,CAAW,CAAC,cACpC1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,qBAAS,CAAW,CAAC,cAChC1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,oBAAU,CAAW,CAAC,cACjC1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,kBAAQ,CAAW,CAAC,cAC/B1D,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAC,aAAQ,CAAW,CAAC,EACvB,CAAC,CACF,CAAC,cACZ1D,IAAA,CAAC3B,SAAS,EAAAqF,QAAA,CACP/C,iBAAiB,CAAC4E,GAAG,CAAEzC,QAAQ,eAC9B5C,KAAA,CAACzB,QAAQ,EAAAiF,QAAA,eACPxD,KAAA,CAAC5B,SAAS,EAAAoF,QAAA,EAAC,GAAC,CAACZ,QAAQ,CAACG,EAAE,EAAY,CAAC,cACrCjD,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAEZ,QAAQ,CAACK,YAAY,uBAAAG,MAAA,CAAmBR,QAAQ,CAAC8C,UAAU,CAAE,CAAY,CAAC,cACtF5F,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAE7D,mBAAmB,CAACiD,QAAQ,CAACkD,YAAY,CAAC,CAAY,CAAC,cACnEhG,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAE7D,mBAAmB,CAACiD,QAAQ,CAACmD,UAAU,CAAC,CAAY,CAAC,cACjEjG,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAE5D,cAAc,CAACgD,QAAQ,CAACqD,WAAW,CAAC,CAAY,CAAC,cAC7DnG,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,cACR1D,IAAA,CAACtB,IAAI,EACH+G,KAAK,CAAEjG,iBAAiB,CAACsD,QAAQ,CAACW,MAAM,EAAI,CAAC,CAAE,CAC/CU,KAAK,CAAEX,cAAc,CAACV,QAAQ,CAACW,MAAM,EAAI,CAAC,CAAE,CAC5CiC,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZ1F,IAAA,CAAC1B,SAAS,EAAAoF,QAAA,CAAE7D,mBAAmB,CAACiD,QAAQ,CAAClB,SAAS,EAAI,EAAE,CAAC,CAAY,CAAC,cACtE1B,KAAA,CAAC5B,SAAS,EAAAoF,QAAA,eACR1D,IAAA,CAACrB,UAAU,EACTwF,KAAK,CAAC,SAAS,CACfE,OAAO,CAAEA,CAAA,GAAMhB,kBAAkB,CAACP,QAAQ,CAACG,EAAG,CAAE,CAChDe,KAAK,CAAC,KAAK,CAAAN,QAAA,cAEX1D,IAAA,CAACV,cAAc,GAAE,CAAC,CACR,CAAC,cACbU,IAAA,CAACrB,UAAU,EACTwF,KAAK,CAAC,WAAW,CACjBE,OAAO,CAAEA,CAAA,GAAMd,kBAAkB,CAACT,QAAQ,CAACG,EAAG,CAAE,CAChDe,KAAK,CAAC,UAAK,CAAAN,QAAA,cAEX1D,IAAA,CAACT,QAAQ,GAAE,CAAC,CACF,CAAC,EACJ,CAAC,GA7BCuD,QAAQ,CAACG,EA8Bd,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CACjB,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}