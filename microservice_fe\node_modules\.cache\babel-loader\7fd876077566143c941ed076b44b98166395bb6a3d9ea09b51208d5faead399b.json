{"ast": null, "code": "import { normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport { formatDateForInput } from '../../utils/dateUtils';\nconst BASE_URL = '/api/customer-statistics';\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate, endDate) => {\n    try {\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      const result = await get(`${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('API result:', result);\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          const normalizedData = result.map(customer => {\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      throw error;\n    }\n  },\n  // Get customer invoices\n  getCustomerInvoices: async (customerId, startDate, endDate) => {\n    try {\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      const result = await get(`${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Invoices result:', result);\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        return result.map(invoice => ({\n          ...invoice,\n          id: invoice.id || 0,\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          return [result];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      throw error;\n    }\n  },\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate, endDate) => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', {\n        startDate: formattedStartDate,\n        endDate: formattedEndDate\n      });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get(`${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          // 30 second timeout\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(`http://localhost:8085/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`, {\n          timeout: 30000,\n          headers: {\n            'Accept': 'application/json',\n            'Content-Type': 'application/json'\n          }\n        });\n        result = directResponse.data;\n      }\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = date => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 12, 0, 0);\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};", "map": {"version": 3, "names": ["normalizeCustomerRevenue", "normalizeTimeBasedRevenue", "get", "formatDateForInput", "BASE_URL", "customerStatisticsService", "getCustomerRevenueStatistics", "startDate", "endDate", "formattedStartDate", "formatDateToString", "formattedEndDate", "console", "log", "result", "error", "Array", "isArray", "normalizedData", "map", "customer", "filter", "err", "warn", "singleItem", "getCustomerInvoices", "customerId", "invoice", "id", "paymentAmount", "paymentDate", "Date", "getDailyRevenueStatistics", "isConnected", "checkApiConnection", "Error", "timeout", "headers", "gatewayError", "directResponse", "axios", "data", "item", "sort", "a", "b", "dateA", "date", "dateB", "getTime", "getWeeklyRevenueStatistics", "getMonthlyRevenueStatistics", "getYearlyRevenueStatistics", "isNaN", "today", "adjustedDate", "getFullYear", "getMonth", "getDate"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/services/statistics/customerStatisticsService.ts"], "sourcesContent": ["import { CustomerRevenue, CustomerPayment, TimeBasedRevenue, normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';\nimport { get } from '../api/apiClient';\nimport { formatDateForInput } from '../../utils/dateUtils';\n\nconst BASE_URL = '/api/customer-statistics';\n\nexport const customerStatisticsService = {\n  // Get customer revenue statistics\n  getCustomerRevenueStatistics: async (startDate: Date, endDate: Date): Promise<CustomerRevenue[]> => {\n    try {\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n\n      const result = await get<CustomerRevenue[]>(\n        `${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`\n      );\n\n      console.log('API result:', result);\n\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          const normalizedData = result.map(customer => {\n            if (!customer) return normalizeCustomerRevenue(null);\n            return normalizeCustomerRevenue(customer);\n          }).filter(customer => customer !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping customer revenue data:', err);\n          return [];\n        }\n      }\n\n      // If result is not an array, try to convert it\n      if (typeof result === 'object') {\n        console.warn('Result is not an array, attempting to convert:', result);\n        try {\n          const singleItem = normalizeCustomerRevenue(result);\n          return [singleItem];\n        } catch (err) {\n          console.error('Failed to convert object to array:', err);\n        }\n      }\n\n      console.error('Invalid result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerRevenueStatistics:', error);\n      throw error;\n    }\n  },\n\n  // Get customer invoices\n  getCustomerInvoices: async (customerId: number, startDate: Date, endDate: Date): Promise<CustomerPayment[]> => {\n    try {\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n\n      const result = await get<CustomerPayment[]>(\n        `${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`\n      );\n\n      console.log('Invoices result:', result);\n\n      if (!result) {\n        console.error('API returned null or undefined result for invoices');\n        return [];\n      }\n\n      // Ensure we have a valid array\n      if (Array.isArray(result)) {\n        return result.map(invoice => ({\n          ...invoice,\n          id: invoice.id || 0,\n          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,\n          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()\n        })).filter(invoice => invoice !== null);\n      }\n\n      // If result is not an array but is an object, try to convert it\n      if (typeof result === 'object' && result !== null) {\n        console.warn('Invoices result is not an array, attempting to convert:', result);\n        try {\n          return [result as CustomerPayment];\n        } catch (err) {\n          console.error('Failed to convert invoice object to array:', err);\n        }\n      }\n\n      console.error('Invalid invoices result format:', result);\n      return [];\n    } catch (error) {\n      console.error('Error in getCustomerInvoices:', error);\n      throw error;\n    }\n  },\n\n  // Get daily revenue statistics\n  getDailyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping daily revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching daily revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get weekly revenue statistics\n  getWeeklyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          }).filter(item => item !== null);\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping weekly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching weekly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get monthly revenue statistics\n  getMonthlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo ngày tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping monthly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching monthly revenue statistics:', error);\n      throw error;\n    }\n  },\n\n  // Get yearly revenue statistics\n  getYearlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {\n    try {\n      // Kiểm tra kết nối API trước khi gọi\n      const isConnected = await checkApiConnection();\n      if (!isConnected) {\n        console.error('API connection failed. Service might be down.');\n        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');\n      }\n\n      const formattedStartDate = formatDateToString(startDate);\n      const formattedEndDate = formatDateToString(endDate);\n\n      // Log chi tiết thông tin request\n      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);\n      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });\n\n      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại\n      let result;\n      try {\n        // Tăng timeout để tránh lỗi timeout\n        result = await get<TimeBasedRevenue[]>(\n          `${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000, // 30 second timeout\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json',\n              'Cache-Control': 'no-cache'\n            }\n          }\n        );\n      } catch (gatewayError) {\n        console.error('Failed to get data through API Gateway:', gatewayError);\n        console.log('Trying direct connection to service...');\n\n        // Thử kết nối trực tiếp đến service\n        const directResponse = await axios.get(\n          `http://localhost:8085/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,\n          {\n            timeout: 30000,\n            headers: {\n              'Accept': 'application/json',\n              'Content-Type': 'application/json'\n            }\n          }\n        );\n\n        result = directResponse.data;\n      }\n\n      console.log('API result:', result);\n\n      // Kiểm tra kết quả trả về\n      if (!result) {\n        console.error('API returned null or undefined result');\n        return [];\n      }\n\n      // Normalize the data using our helper function\n      if (Array.isArray(result)) {\n        try {\n          // Xử lý từng phần tử một cách an toàn\n          const normalizedData = result.map(item => {\n            // Đảm bảo item không null\n            if (!item) return normalizeTimeBasedRevenue(null);\n            return normalizeTimeBasedRevenue(item);\n          })\n          .filter(item => item !== null)\n          // Sắp xếp theo năm tăng dần\n          .sort((a, b) => {\n            const dateA = new Date(a.date);\n            const dateB = new Date(b.date);\n            return dateA.getTime() - dateB.getTime();\n          });\n\n          console.log('Normalized data:', normalizedData);\n          return normalizedData;\n        } catch (err) {\n          console.error('Error mapping yearly revenue data:', err);\n          return [];\n        }\n      }\n\n      // Fallback if result is not an array\n      console.error('API result is not an array:', result);\n      return [];\n    } catch (error) {\n      console.error('Error fetching yearly revenue statistics:', error);\n      throw error;\n    }\n  }\n};\n\n// Helper function to format date to string in ISO format (yyyy-MM-dd)\nconst formatDateToString = (date: Date): string => {\n  try {\n    // Ensure we have a valid date\n    if (!(date instanceof Date) || isNaN(date.getTime())) {\n      console.error('Invalid date provided:', date);\n      // Return today's date as fallback\n      const today = new Date();\n      return formatDateForInput(today);\n    }\n\n    // Xử lý đặc biệt để tránh vấn đề múi giờ\n    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ\n    const adjustedDate = new Date(\n      date.getFullYear(),\n      date.getMonth(),\n      date.getDate(),\n      12, 0, 0\n    );\n\n    // Format to ISO date string using our utility function\n    return formatDateForInput(adjustedDate);\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    // Return today's date as fallback\n    const today = new Date();\n    return formatDateForInput(today);\n  }\n};\n"], "mappings": "AAAA,SAA6DA,wBAAwB,EAAEC,yBAAyB,QAAQ,cAAc;AACtI,SAASC,GAAG,QAAQ,kBAAkB;AACtC,SAASC,kBAAkB,QAAQ,uBAAuB;AAE1D,MAAMC,QAAQ,GAAG,0BAA0B;AAE3C,OAAO,MAAMC,yBAAyB,GAAG;EACvC;EACAC,4BAA4B,EAAE,MAAAA,CAAOC,SAAe,EAAEC,OAAa,KAAiC;IAClG,IAAI;MACF,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACH,SAAS,CAAC;MACxD,MAAMI,gBAAgB,GAAGD,kBAAkB,CAACF,OAAO,CAAC;MAEpDI,OAAO,CAACC,GAAG,CAAC,gBAAgBT,QAAQ,sBAAsBK,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAE3G,MAAMG,MAAM,GAAG,MAAMZ,GAAG,CACtB,GAAGE,QAAQ,sBAAsBK,kBAAkB,YAAYE,gBAAgB,EACjF,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;MAElC,IAAI,CAACA,MAAM,EAAE;QACXF,OAAO,CAACG,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACC,QAAQ,IAAI;YAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOpB,wBAAwB,CAAC,IAAI,CAAC;YACpD,OAAOA,wBAAwB,CAACoB,QAAQ,CAAC;UAC3C,CAAC,CAAC,CAACC,MAAM,CAACD,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC;UAExCR,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAEO,GAAG,CAAC;UAC1D,OAAO,EAAE;QACX;MACF;;MAEA;MACA,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;QAC9BF,OAAO,CAACW,IAAI,CAAC,gDAAgD,EAAET,MAAM,CAAC;QACtE,IAAI;UACF,MAAMU,UAAU,GAAGxB,wBAAwB,CAACc,MAAM,CAAC;UACnD,OAAO,CAACU,UAAU,CAAC;QACrB,CAAC,CAAC,OAAOF,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAEO,GAAG,CAAC;QAC1D;MACF;MAEAV,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAED,MAAM,CAAC;MAC/C,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAU,mBAAmB,EAAE,MAAAA,CAAOC,UAAkB,EAAEnB,SAAe,EAAEC,OAAa,KAAiC;IAC7G,IAAI;MACF,MAAMC,kBAAkB,GAAGC,kBAAkB,CAACH,SAAS,CAAC;MACxD,MAAMI,gBAAgB,GAAGD,kBAAkB,CAACF,OAAO,CAAC;MAEpDI,OAAO,CAACC,GAAG,CAAC,gBAAgBT,QAAQ,aAAasB,UAAU,uBAAuBjB,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAEnI,MAAMG,MAAM,GAAG,MAAMZ,GAAG,CACtB,GAAGE,QAAQ,aAAasB,UAAU,uBAAuBjB,kBAAkB,YAAYE,gBAAgB,EACzG,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,MAAM,CAAC;MAEvC,IAAI,CAACA,MAAM,EAAE;QACXF,OAAO,CAACG,KAAK,CAAC,oDAAoD,CAAC;QACnE,OAAO,EAAE;MACX;;MAEA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,OAAOA,MAAM,CAACK,GAAG,CAACQ,OAAO,KAAK;UAC5B,GAAGA,OAAO;UACVC,EAAE,EAAED,OAAO,CAACC,EAAE,IAAI,CAAC;UACnBC,aAAa,EAAE,OAAOF,OAAO,CAACE,aAAa,KAAK,QAAQ,GAAGF,OAAO,CAACE,aAAa,GAAG,CAAC;UACpFC,WAAW,EAAEH,OAAO,CAACG,WAAW,GAAG,IAAIC,IAAI,CAACJ,OAAO,CAACG,WAAW,CAAC,GAAG,IAAIC,IAAI,CAAC;QAC9E,CAAC,CAAC,CAAC,CAACV,MAAM,CAACM,OAAO,IAAIA,OAAO,KAAK,IAAI,CAAC;MACzC;;MAEA;MACA,IAAI,OAAOb,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;QACjDF,OAAO,CAACW,IAAI,CAAC,yDAAyD,EAAET,MAAM,CAAC;QAC/E,IAAI;UACF,OAAO,CAACA,MAAM,CAAoB;QACpC,CAAC,CAAC,OAAOQ,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,4CAA4C,EAAEO,GAAG,CAAC;QAClE;MACF;MAEAV,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAED,MAAM,CAAC;MACxD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAiB,yBAAyB,EAAE,MAAAA,CAAOzB,SAAe,EAAEC,OAAa,KAAkC;IAChG,IAAI;MACF;MACA,MAAMyB,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBrB,OAAO,CAACG,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIoB,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAM1B,kBAAkB,GAAGC,kBAAkB,CAACH,SAAS,CAAC;MACxD,MAAMI,gBAAgB,GAAGD,kBAAkB,CAACF,OAAO,CAAC;;MAEpD;MACAI,OAAO,CAACC,GAAG,CAAC,gBAAgBT,QAAQ,4BAA4BK,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACjHC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEN,SAAS,EAAEE,kBAAkB;QAAED,OAAO,EAAEG;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIG,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMZ,GAAG,CAChB,GAAGE,QAAQ,4BAA4BK,kBAAkB,YAAYE,gBAAgB,EAAE,EACvF;UACEyB,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB1B,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuB,YAAY,CAAC;QACtE1B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM0B,cAAc,GAAG,MAAMC,KAAK,CAACtC,GAAG,CACpC,yEAAyEO,kBAAkB,YAAYE,gBAAgB,EAAE,EACzH;UACEyB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDvB,MAAM,GAAGyB,cAAc,CAACE,IAAI;MAC9B;MAEA7B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXF,OAAO,CAACG,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACuB,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOzC,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACyC,IAAI,CAAC;UACxC,CAAC,CAAC,CACDrB,MAAM,CAACqB,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIf,IAAI,CAACa,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIjB,IAAI,CAACc,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEO,GAAG,CAAC;UACvD,OAAO,EAAE;QACX;MACF;;MAEA;MACAV,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAED,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAmC,0BAA0B,EAAE,MAAAA,CAAO3C,SAAe,EAAEC,OAAa,KAAkC;IACjG,IAAI;MACF;MACA,MAAMyB,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBrB,OAAO,CAACG,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIoB,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAM1B,kBAAkB,GAAGC,kBAAkB,CAACH,SAAS,CAAC;MACxD,MAAMI,gBAAgB,GAAGD,kBAAkB,CAACF,OAAO,CAAC;;MAEpD;MACAI,OAAO,CAACC,GAAG,CAAC,gBAAgBT,QAAQ,6BAA6BK,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAClHC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEN,SAAS,EAAEE,kBAAkB;QAAED,OAAO,EAAEG;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIG,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMZ,GAAG,CAChB,GAAGE,QAAQ,6BAA6BK,kBAAkB,YAAYE,gBAAgB,EAAE,EACxF;UACEyB,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB1B,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuB,YAAY,CAAC;QACtE1B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM0B,cAAc,GAAG,MAAMC,KAAK,CAACtC,GAAG,CACpC,0EAA0EO,kBAAkB,YAAYE,gBAAgB,EAAE,EAC1H;UACEyB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDvB,MAAM,GAAGyB,cAAc,CAACE,IAAI;MAC9B;MAEA7B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXF,OAAO,CAACG,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACuB,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOzC,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACyC,IAAI,CAAC;UACxC,CAAC,CAAC,CAACrB,MAAM,CAACqB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC;UAEhC9B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAEO,GAAG,CAAC;UACxD,OAAO,EAAE;QACX;MACF;;MAEA;MACAV,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAED,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAoC,2BAA2B,EAAE,MAAAA,CAAO5C,SAAe,EAAEC,OAAa,KAAkC;IAClG,IAAI;MACF;MACA,MAAMyB,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBrB,OAAO,CAACG,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIoB,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAM1B,kBAAkB,GAAGC,kBAAkB,CAACH,SAAS,CAAC;MACxD,MAAMI,gBAAgB,GAAGD,kBAAkB,CAACF,OAAO,CAAC;;MAEpD;MACAI,OAAO,CAACC,GAAG,CAAC,gBAAgBT,QAAQ,8BAA8BK,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MACnHC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEN,SAAS,EAAEE,kBAAkB;QAAED,OAAO,EAAEG;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIG,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMZ,GAAG,CAChB,GAAGE,QAAQ,8BAA8BK,kBAAkB,YAAYE,gBAAgB,EAAE,EACzF;UACEyB,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB1B,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuB,YAAY,CAAC;QACtE1B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM0B,cAAc,GAAG,MAAMC,KAAK,CAACtC,GAAG,CACpC,2EAA2EO,kBAAkB,YAAYE,gBAAgB,EAAE,EAC3H;UACEyB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDvB,MAAM,GAAGyB,cAAc,CAACE,IAAI;MAC9B;MAEA7B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXF,OAAO,CAACG,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACuB,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOzC,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACyC,IAAI,CAAC;UACxC,CAAC,CAAC,CACDrB,MAAM,CAACqB,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIf,IAAI,CAACa,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIjB,IAAI,CAACc,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEO,GAAG,CAAC;UACzD,OAAO,EAAE;QACX;MACF;;MAEA;MACAV,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAED,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF,CAAC;EAED;EACAqC,0BAA0B,EAAE,MAAAA,CAAO7C,SAAe,EAAEC,OAAa,KAAkC;IACjG,IAAI;MACF;MACA,MAAMyB,WAAW,GAAG,MAAMC,kBAAkB,CAAC,CAAC;MAC9C,IAAI,CAACD,WAAW,EAAE;QAChBrB,OAAO,CAACG,KAAK,CAAC,+CAA+C,CAAC;QAC9D,MAAM,IAAIoB,KAAK,CAAC,wEAAwE,CAAC;MAC3F;MAEA,MAAM1B,kBAAkB,GAAGC,kBAAkB,CAACH,SAAS,CAAC;MACxD,MAAMI,gBAAgB,GAAGD,kBAAkB,CAACF,OAAO,CAAC;;MAEpD;MACAI,OAAO,CAACC,GAAG,CAAC,gBAAgBT,QAAQ,6BAA6BK,kBAAkB,YAAYE,gBAAgB,EAAE,CAAC;MAClHC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAEN,SAAS,EAAEE,kBAAkB;QAAED,OAAO,EAAEG;MAAiB,CAAC,CAAC;;MAEhG;MACA,IAAIG,MAAM;MACV,IAAI;QACF;QACAA,MAAM,GAAG,MAAMZ,GAAG,CAChB,GAAGE,QAAQ,6BAA6BK,kBAAkB,YAAYE,gBAAgB,EAAE,EACxF;UACEyB,OAAO,EAAE,KAAK;UAAE;UAChBC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE;UACnB;QACF,CACF,CAAC;MACH,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB1B,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuB,YAAY,CAAC;QACtE1B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM0B,cAAc,GAAG,MAAMC,KAAK,CAACtC,GAAG,CACpC,0EAA0EO,kBAAkB,YAAYE,gBAAgB,EAAE,EAC1H;UACEyB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE;YACP,QAAQ,EAAE,kBAAkB;YAC5B,cAAc,EAAE;UAClB;QACF,CACF,CAAC;QAEDvB,MAAM,GAAGyB,cAAc,CAACE,IAAI;MAC9B;MAEA7B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACA,IAAI,CAACA,MAAM,EAAE;QACXF,OAAO,CAACG,KAAK,CAAC,uCAAuC,CAAC;QACtD,OAAO,EAAE;MACX;;MAEA;MACA,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;QACzB,IAAI;UACF;UACA,MAAMI,cAAc,GAAGJ,MAAM,CAACK,GAAG,CAACuB,IAAI,IAAI;YACxC;YACA,IAAI,CAACA,IAAI,EAAE,OAAOzC,yBAAyB,CAAC,IAAI,CAAC;YACjD,OAAOA,yBAAyB,CAACyC,IAAI,CAAC;UACxC,CAAC,CAAC,CACDrB,MAAM,CAACqB,IAAI,IAAIA,IAAI,KAAK,IAAI;UAC7B;UAAA,CACCC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACd,MAAMC,KAAK,GAAG,IAAIf,IAAI,CAACa,CAAC,CAACG,IAAI,CAAC;YAC9B,MAAMC,KAAK,GAAG,IAAIjB,IAAI,CAACc,CAAC,CAACE,IAAI,CAAC;YAC9B,OAAOD,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGD,KAAK,CAACC,OAAO,CAAC,CAAC;UAC1C,CAAC,CAAC;UAEFrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,cAAc,CAAC;UAC/C,OAAOA,cAAc;QACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZV,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAEO,GAAG,CAAC;UACxD,OAAO,EAAE;QACX;MACF;;MAEA;MACAV,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAED,MAAM,CAAC;MACpD,OAAO,EAAE;IACX,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;AACF,CAAC;;AAED;AACA,MAAML,kBAAkB,GAAIqC,IAAU,IAAa;EACjD,IAAI;IACF;IACA,IAAI,EAAEA,IAAI,YAAYhB,IAAI,CAAC,IAAIsB,KAAK,CAACN,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MACpDrC,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEgC,IAAI,CAAC;MAC7C;MACA,MAAMO,KAAK,GAAG,IAAIvB,IAAI,CAAC,CAAC;MACxB,OAAO5B,kBAAkB,CAACmD,KAAK,CAAC;IAClC;;IAEA;IACA;IACA,MAAMC,YAAY,GAAG,IAAIxB,IAAI,CAC3BgB,IAAI,CAACS,WAAW,CAAC,CAAC,EAClBT,IAAI,CAACU,QAAQ,CAAC,CAAC,EACfV,IAAI,CAACW,OAAO,CAAC,CAAC,EACd,EAAE,EAAE,CAAC,EAAE,CACT,CAAC;;IAED;IACA,OAAOvD,kBAAkB,CAACoD,YAAY,CAAC;EACzC,CAAC,CAAC,OAAOxC,KAAK,EAAE;IACdH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C;IACA,MAAMuC,KAAK,GAAG,IAAIvB,IAAI,CAAC,CAAC;IACxB,OAAO5B,kBAAkB,CAACmD,KAAK,CAAC;EAClC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}